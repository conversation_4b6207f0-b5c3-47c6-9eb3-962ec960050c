# Free AI Models Setup Guide

This guide shows you how to set up the AI Code Assistant using **completely free** AI models for testing and development.

## 🆓 Free AI Provider Options

### 1. Ollama (Recommended) - Local & Free

**Best for**: Privacy, no API limits, works offline
**Requirements**: ~4GB RAM, ~4GB disk space

#### Installation:
1. **Install Ollama**:
   ```bash
   # Windows/Mac: Download from https://ollama.ai/
   # Linux:
   curl -fsSL https://ollama.ai/install.sh | sh
   ```

2. **Pull a code model**:
   ```bash
   # Recommended for coding (7B model, ~4GB)
   ollama pull codellama:7b
   
   # Alternatives:
   ollama pull llama2:7b        # General purpose
   ollama pull mistral:7b       # Good balance
   ollama pull codellama:13b    # Better but larger (8GB)
   ```

3. **Configure the assistant**:
   ```bash
   cd python-backend
   cp .env.example .env
   ```
   
   Edit `.env`:
   ```
   AI_PROVIDER=ollama
   AI_MODEL=codellama:7b
   OLLAMA_URL=http://localhost:11434
   ```

4. **Start Ollama** (if not auto-started):
   ```bash
   ollama serve
   ```

### 2. Groq - Free API (Rate Limited)

**Best for**: Cloud-based, fast inference, no local resources needed
**Limits**: Free tier with rate limits

#### Setup:
1. **Get free API key**:
   - Visit https://console.groq.com/
   - Sign up for free account
   - Create API key

2. **Configure**:
   Edit `.env`:
   ```
   AI_PROVIDER=groq
   AI_MODEL=mixtral-8x7b-32768
   GROQ_API_KEY=your_free_api_key_here
   ```

### 3. Hugging Face Transformers - Local & Free

**Best for**: Completely offline, customizable models
**Requirements**: ~8GB RAM, GPU recommended

#### Setup:
1. **Install additional dependencies**:
   ```bash
   pip install transformers torch accelerate
   ```

2. **Configure**:
   Edit `.env`:
   ```
   AI_PROVIDER=huggingface
   HF_MODEL=microsoft/DialoGPT-medium
   ```

## 🚀 Quick Start (Ollama)

Here's the fastest way to get started with free AI:

```bash
# 1. Install Ollama
curl -fsSL https://ollama.ai/install.sh | sh  # Linux/Mac
# Or download from https://ollama.ai/ for Windows

# 2. Pull a model
ollama pull codellama:7b

# 3. Clone and setup the project
git clone <your-repo>
cd ai-code-assistant

# 4. Install minimal dependencies
cd python-backend
pip install -r requirements-free.txt

# 5. Configure for Ollama
cp .env.example .env
# Edit .env to set AI_PROVIDER=ollama

# 6. Start the backend
python main.py

# 7. Install VS Code extension
cd ..
npm install
npm run compile
# Install the .vsix file in VS Code
```

## 📊 Model Comparison

| Provider | Model | Size | Speed | Quality | Cost | Offline |
|----------|-------|------|-------|---------|------|---------|
| Ollama | codellama:7b | 4GB | Medium | Good | Free | ✅ |
| Ollama | codellama:13b | 8GB | Slow | Better | Free | ✅ |
| Groq | mixtral-8x7b | - | Fast | Good | Free* | ❌ |
| HuggingFace | DialoGPT-medium | 2GB | Slow | Fair | Free | ✅ |

*Rate limited

## 🔧 Configuration Examples

### Ollama Configuration
```env
AI_PROVIDER=ollama
AI_MODEL=codellama:7b
OLLAMA_URL=http://localhost:11434
```

### Groq Configuration
```env
AI_PROVIDER=groq
AI_MODEL=mixtral-8x7b-32768
GROQ_API_KEY=gsk_your_free_key_here
```

### Hugging Face Configuration
```env
AI_PROVIDER=huggingface
HF_MODEL=microsoft/DialoGPT-medium
```

## 🛠️ Troubleshooting

### Ollama Issues

**"Connection refused"**:
```bash
# Start Ollama service
ollama serve

# Check if running
curl http://localhost:11434/api/tags
```

**"Model not found"**:
```bash
# List installed models
ollama list

# Pull the model
ollama pull codellama:7b
```

### Groq Issues

**"API key invalid"**:
- Check your API key at https://console.groq.com/
- Ensure no extra spaces in .env file

**"Rate limit exceeded"**:
- Free tier has limits
- Wait a few minutes and try again
- Consider using Ollama for unlimited usage

### Hugging Face Issues

**"Out of memory"**:
```bash
# Use smaller model
HF_MODEL=microsoft/DialoGPT-small

# Or use CPU-only
# Edit ai_handler.py to force device=-1
```

**"Model download slow"**:
- Models download on first use
- Be patient, it's a one-time download
- Use faster internet connection

## 🎯 Recommended Setup for Different Use Cases

### **For Learning/Testing**:
- **Provider**: Ollama
- **Model**: codellama:7b
- **Why**: Free, unlimited, good for learning

### **For Development**:
- **Provider**: Groq (with Ollama fallback)
- **Model**: mixtral-8x7b-32768
- **Why**: Fast responses, good quality

### **For Privacy/Offline**:
- **Provider**: Ollama
- **Model**: codellama:13b (if you have 8GB+ RAM)
- **Why**: Completely offline, no data sent anywhere

### **For Low Resources**:
- **Provider**: Groq
- **Model**: mixtral-8x7b-32768
- **Why**: No local resources needed

## 🔄 Switching Between Providers

You can easily switch between providers by changing the `.env` file:

```bash
# Switch to Ollama
AI_PROVIDER=ollama
AI_MODEL=codellama:7b

# Switch to Groq
AI_PROVIDER=groq
AI_MODEL=mixtral-8x7b-32768
GROQ_API_KEY=your_key

# Restart the backend
python main.py
```

## 📈 Performance Tips

1. **For Ollama**:
   - Use SSD storage for better model loading
   - Close other applications to free RAM
   - Use 13B models only if you have 8GB+ RAM

2. **For Groq**:
   - Batch similar requests
   - Be mindful of rate limits
   - Cache responses when possible

3. **For Hugging Face**:
   - Use GPU if available
   - Start with smaller models
   - Consider quantized models for speed

## 🆙 Upgrading to Paid Models

When you're ready for better performance:

1. **OpenAI**: Best overall quality
2. **Anthropic Claude**: Great for code analysis
3. **Groq Paid**: Faster rate limits

Just update your `.env` file and install the required packages:

```bash
# For OpenAI
pip install openai
# Set AI_PROVIDER=openai and OPENAI_API_KEY

# For Anthropic
pip install anthropic
# Set AI_PROVIDER=anthropic and ANTHROPIC_API_KEY
```

## 🎉 You're Ready!

With any of these free setups, you'll have:
- ✅ AI-powered code completion
- ✅ Interactive chat assistance
- ✅ Code explanation and refactoring
- ✅ Test generation
- ✅ Bug detection and fixing

Start coding with AI assistance - completely free! 🚀
