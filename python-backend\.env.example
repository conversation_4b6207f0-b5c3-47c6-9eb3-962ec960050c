# AI Provider Configuration
# Options: ollama, groq, huggingface, openai, anthropic
AI_PROVIDER=groq

# Model Configuration (depends on provider)
# AI_MODEL=codellama:7b  # For Ollama: codellama:7b, llama2:7b, mistral:7b
AI_MODEL=mixtral-8x7b-32768  # For Groq
# AI_MODEL=microsoft/DialoGPT-medium  # For Hugging Face

# === FREE AI PROVIDERS ===

# Ollama (Local, Free) - RECOMMENDED FOR TESTING
OLLAMA_URL=http://localhost:11434
# Install: https://ollama.ai/
# Models: ollama pull codellama:7b

# Groq (Free API with rate limits)
GROQ_API_KEY=********************************************************
# Get free key: https://console.groq.com/

# Hugging Face (Local, Free)
HF_MODEL=microsoft/DialoGPT-medium
# Alternatives: microsoft/CodeGPT-small-py, huggingface/CodeBERTa-small-v1

# === PAID AI PROVIDERS ===

# OpenAI Configuration (Paid)
# OPENAI_API_KEY=your_openai_api_key_here

# Anthropic Configuration (Paid)
# ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Server Configuration
HOST=127.0.0.1
PORT=8000
LOG_LEVEL=info

# Development Settings
DEBUG=false
RELOAD=false

# Local Model Configuration (for Ollama or similar)
LOCAL_MODEL_URL=http://localhost:11434
LOCAL_MODEL_NAME=codellama

# Feature Flags
ENABLE_CODE_ANALYSIS=true
ENABLE_COMPLETIONS=true
ENABLE_CHAT=true
ENABLE_DIAGNOSTICS=true

# Performance Settings
MAX_CONTEXT_LENGTH=4000
MAX_COMPLETION_TOKENS=500
COMPLETION_TEMPERATURE=0.7
ANALYSIS_TEMPERATURE=0.3

# Cache Settings
ENABLE_CACHE=true
CACHE_TTL=3600  # seconds

# Security Settings
ALLOWED_ORIGINS=*
CORS_ENABLED=true
