# AI Provider Configuration
AI_PROVIDER=openai  # Options: openai, anthropic, local
AI_MODEL=gpt-4      # Model to use (gpt-4, gpt-3.5-turbo, claude-3-sonnet, etc.)

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Anthropic Configuration
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Server Configuration
HOST=127.0.0.1
PORT=8000
LOG_LEVEL=info

# Development Settings
DEBUG=false
RELOAD=false

# Local Model Configuration (for Ollama or similar)
LOCAL_MODEL_URL=http://localhost:11434
LOCAL_MODEL_NAME=codellama

# Feature Flags
ENABLE_CODE_ANALYSIS=true
ENABLE_COMPLETIONS=true
ENABLE_CHAT=true
ENABLE_DIAGNOSTICS=true

# Performance Settings
MAX_CONTEXT_LENGTH=4000
MAX_COMPLETION_TOKENS=500
COMPLETION_TEMPERATURE=0.7
ANALYSIS_TEMPERATURE=0.3

# Cache Settings
ENABLE_CACHE=true
CACHE_TTL=3600  # seconds

# Security Settings
ALLOWED_ORIGINS=*
CORS_ENABLED=true
