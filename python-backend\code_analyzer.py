"""
Code Analyzer for parsing and understanding code structure
"""

import ast
import logging
import re
from typing import Dict, List, Optional, Any, Tuple
import json

try:
    import tree_sitter
    from tree_sitter import Language, Parser
    TREE_SITTER_AVAILABLE = True
except ImportError:
    TREE_SITTER_AVAILABLE = False
    logging.warning("Tree-sitter not available. Using basic AST parsing only.")

logger = logging.getLogger(__name__)


class CodeAnalyzer:
    """Analyzes code structure and provides context for AI processing"""
    
    def __init__(self):
        self.parsers = {}
        self._initialize_parsers()
    
    def _initialize_parsers(self):
        """Initialize tree-sitter parsers for different languages"""
        if not TREE_SITTER_AVAILABLE:
            return
        
        try:
            # This is a simplified setup - in production, you'd need to build the languages
            # For now, we'll use Python's AST for Python code and basic parsing for others
            pass
        except Exception as e:
            logger.error(f"Error initializing parsers: {e}")
    
    async def analyze(self, code: str, language: str) -> Dict[str, Any]:
        """Analyze code structure and extract relevant information"""
        try:
            if language.lower() == "python":
                return await self._analyze_python(code)
            elif language.lower() in ["javascript", "typescript"]:
                return await self._analyze_javascript(code)
            elif language.lower() in ["java"]:
                return await self._analyze_java(code)
            else:
                return await self._analyze_generic(code, language)
                
        except Exception as e:
            logger.error(f"Error analyzing {language} code: {e}")
            return {"error": str(e), "language": language}
    
    async def get_completion_context(self, code: str, language: str, position: Dict[str, int]) -> str:
        """Get context around a specific position for code completion"""
        try:
            lines = code.split('\n')
            line_num = position.get("line", 0)
            char_pos = position.get("character", 0)
            
            # Get context window (5 lines before and after)
            start_line = max(0, line_num - 5)
            end_line = min(len(lines), line_num + 5)
            
            context_lines = lines[start_line:end_line + 1]
            
            # Mark the current position
            if 0 <= line_num < len(lines):
                current_line = lines[line_num]
                before_cursor = current_line[:char_pos]
                after_cursor = current_line[char_pos:]
                context_lines[line_num - start_line] = before_cursor + "<CURSOR>" + after_cursor
            
            return '\n'.join(context_lines)
            
        except Exception as e:
            logger.error(f"Error getting completion context: {e}")
            return code
    
    async def _analyze_python(self, code: str) -> Dict[str, Any]:
        """Analyze Python code using AST"""
        try:
            tree = ast.parse(code)
            
            analysis = {
                "language": "python",
                "functions": [],
                "classes": [],
                "imports": [],
                "variables": [],
                "complexity": 0,
                "lines_of_code": len(code.split('\n')),
                "docstrings": []
            }
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    func_info = {
                        "name": node.name,
                        "line": node.lineno,
                        "args": [arg.arg for arg in node.args.args],
                        "decorators": [ast.unparse(d) for d in node.decorator_list],
                        "docstring": ast.get_docstring(node)
                    }
                    analysis["functions"].append(func_info)
                    
                    if func_info["docstring"]:
                        analysis["docstrings"].append(func_info["docstring"])
                
                elif isinstance(node, ast.ClassDef):
                    class_info = {
                        "name": node.name,
                        "line": node.lineno,
                        "bases": [ast.unparse(base) for base in node.bases],
                        "decorators": [ast.unparse(d) for d in node.decorator_list],
                        "docstring": ast.get_docstring(node),
                        "methods": []
                    }
                    
                    # Get methods
                    for item in node.body:
                        if isinstance(item, ast.FunctionDef):
                            class_info["methods"].append(item.name)
                    
                    analysis["classes"].append(class_info)
                    
                    if class_info["docstring"]:
                        analysis["docstrings"].append(class_info["docstring"])
                
                elif isinstance(node, (ast.Import, ast.ImportFrom)):
                    if isinstance(node, ast.Import):
                        for alias in node.names:
                            analysis["imports"].append({
                                "module": alias.name,
                                "alias": alias.asname,
                                "line": node.lineno
                            })
                    else:  # ImportFrom
                        for alias in node.names:
                            analysis["imports"].append({
                                "module": node.module,
                                "name": alias.name,
                                "alias": alias.asname,
                                "line": node.lineno
                            })
                
                elif isinstance(node, ast.Assign):
                    for target in node.targets:
                        if isinstance(target, ast.Name):
                            analysis["variables"].append({
                                "name": target.id,
                                "line": node.lineno,
                                "type": type(node.value).__name__
                            })
                
                # Calculate complexity (simplified)
                if isinstance(node, (ast.If, ast.For, ast.While, ast.Try, ast.With)):
                    analysis["complexity"] += 1
            
            return analysis
            
        except SyntaxError as e:
            return {
                "language": "python",
                "error": f"Syntax error: {e}",
                "line": e.lineno,
                "offset": e.offset
            }
        except Exception as e:
            logger.error(f"Error analyzing Python code: {e}")
            return {"language": "python", "error": str(e)}
    
    async def _analyze_javascript(self, code: str) -> Dict[str, Any]:
        """Analyze JavaScript/TypeScript code"""
        analysis = {
            "language": "javascript",
            "functions": [],
            "classes": [],
            "imports": [],
            "variables": [],
            "exports": [],
            "lines_of_code": len(code.split('\n'))
        }
        
        lines = code.split('\n')
        
        for i, line in enumerate(lines, 1):
            line = line.strip()
            
            # Function declarations
            if re.match(r'function\s+\w+', line):
                match = re.search(r'function\s+(\w+)', line)
                if match:
                    analysis["functions"].append({
                        "name": match.group(1),
                        "line": i,
                        "type": "function"
                    })
            
            # Arrow functions
            elif re.match(r'const\s+\w+\s*=\s*\(.*\)\s*=>', line):
                match = re.search(r'const\s+(\w+)', line)
                if match:
                    analysis["functions"].append({
                        "name": match.group(1),
                        "line": i,
                        "type": "arrow_function"
                    })
            
            # Class declarations
            elif re.match(r'class\s+\w+', line):
                match = re.search(r'class\s+(\w+)', line)
                if match:
                    analysis["classes"].append({
                        "name": match.group(1),
                        "line": i
                    })
            
            # Imports
            elif line.startswith('import'):
                analysis["imports"].append({
                    "statement": line,
                    "line": i
                })
            
            # Exports
            elif line.startswith('export'):
                analysis["exports"].append({
                    "statement": line,
                    "line": i
                })
        
        return analysis
    
    async def _analyze_java(self, code: str) -> Dict[str, Any]:
        """Analyze Java code"""
        analysis = {
            "language": "java",
            "classes": [],
            "methods": [],
            "imports": [],
            "package": None,
            "lines_of_code": len(code.split('\n'))
        }
        
        lines = code.split('\n')
        
        for i, line in enumerate(lines, 1):
            line = line.strip()
            
            # Package declaration
            if line.startswith('package'):
                match = re.search(r'package\s+([\w.]+)', line)
                if match:
                    analysis["package"] = match.group(1)
            
            # Import statements
            elif line.startswith('import'):
                match = re.search(r'import\s+([\w.*]+)', line)
                if match:
                    analysis["imports"].append({
                        "import": match.group(1),
                        "line": i
                    })
            
            # Class declarations
            elif re.search(r'\bclass\s+\w+', line):
                match = re.search(r'class\s+(\w+)', line)
                if match:
                    analysis["classes"].append({
                        "name": match.group(1),
                        "line": i
                    })
            
            # Method declarations
            elif re.search(r'\b(public|private|protected).*\w+\s*\(', line):
                match = re.search(r'\b(\w+)\s*\(', line)
                if match:
                    analysis["methods"].append({
                        "name": match.group(1),
                        "line": i
                    })
        
        return analysis
    
    async def _analyze_generic(self, code: str, language: str) -> Dict[str, Any]:
        """Generic code analysis for unsupported languages"""
        analysis = {
            "language": language,
            "lines_of_code": len(code.split('\n')),
            "functions": [],
            "comments": [],
            "keywords": []
        }
        
        lines = code.split('\n')
        
        # Common patterns across languages
        function_patterns = [
            r'def\s+(\w+)',      # Python
            r'function\s+(\w+)', # JavaScript
            r'func\s+(\w+)',     # Go
            r'fn\s+(\w+)',       # Rust
        ]
        
        comment_patterns = [
            r'//.*',             # C-style comments
            r'#.*',              # Python/Shell comments
            r'/\*.*?\*/',        # Multi-line C-style
        ]
        
        for i, line in enumerate(lines, 1):
            line_stripped = line.strip()
            
            # Look for functions
            for pattern in function_patterns:
                match = re.search(pattern, line_stripped)
                if match:
                    analysis["functions"].append({
                        "name": match.group(1),
                        "line": i
                    })
            
            # Look for comments
            for pattern in comment_patterns:
                if re.search(pattern, line_stripped):
                    analysis["comments"].append({
                        "content": line_stripped,
                        "line": i
                    })
        
        return analysis
