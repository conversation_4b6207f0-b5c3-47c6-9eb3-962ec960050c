"""
AI Handler for processing AI requests and managing different AI providers
"""

import asyncio
import logging
import os
from typing import Dict, List, Optional, Any
import json
import aiohttp
import requests

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

try:
    import anthropic
    ANTHROPIC_AVAILABLE = True
except ImportError:
    ANTHROPIC_AVAILABLE = False

try:
    from transformers import pipeline, AutoTokenizer, AutoModelForCausalLM
    import torch
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False

from dotenv import load_dotenv

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)


class AIHandler:
    """Handles AI model interactions and processing"""

    def __init__(self):
        self.openai_client = None
        self.anthropic_client = None
        self.hf_pipeline = None
        self.current_provider = os.getenv("AI_PROVIDER", "ollama")  # Default to free Ollama
        self.current_model = os.getenv("AI_MODEL", "codellama:7b")
        self.ollama_url = os.getenv("OLLAMA_URL", "http://localhost:11434")
        self.hf_model_name = os.getenv("HF_MODEL", "microsoft/DialoGPT-medium")

        self._initialize_clients()
    
    def _initialize_clients(self):
        """Initialize AI service clients"""
        try:
            # Initialize OpenAI client (if available and configured)
            if OPENAI_AVAILABLE:
                openai_api_key = os.getenv("OPENAI_API_KEY")
                if openai_api_key:
                    self.openai_client = openai.AsyncOpenAI(api_key=openai_api_key)
                    logger.info("OpenAI client initialized")

            # Initialize Anthropic client (if available and configured)
            if ANTHROPIC_AVAILABLE:
                anthropic_api_key = os.getenv("ANTHROPIC_API_KEY")
                if anthropic_api_key:
                    self.anthropic_client = anthropic.AsyncAnthropic(api_key=anthropic_api_key)
                    logger.info("Anthropic client initialized")

            # Initialize Hugging Face pipeline (if available)
            if TRANSFORMERS_AVAILABLE and self.current_provider == "huggingface":
                self._initialize_huggingface()

        except Exception as e:
            logger.error(f"Error initializing AI clients: {e}")

    def _initialize_huggingface(self):
        """Initialize Hugging Face transformers pipeline"""
        try:
            logger.info(f"Loading Hugging Face model: {self.hf_model_name}")
            device = 0 if torch.cuda.is_available() else -1
            self.hf_pipeline = pipeline(
                "text-generation",
                model=self.hf_model_name,
                device=device,
                torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32
            )
            logger.info("Hugging Face pipeline initialized")
        except Exception as e:
            logger.error(f"Error initializing Hugging Face pipeline: {e}")
            self.hf_pipeline = None
    
    async def chat(self, prompt: str, model: Optional[str] = None, context: Optional[str] = None) -> Dict[str, Any]:
        """Handle chat requests"""
        try:
            model = model or self.current_model
            
            # Prepare the full prompt with context
            full_prompt = self._prepare_chat_prompt(prompt, context)
            
            if self.current_provider == "openai" and self.openai_client:
                return await self._chat_openai(full_prompt, model)
            elif self.current_provider == "anthropic" and self.anthropic_client:
                return await self._chat_anthropic(full_prompt, model)
            elif self.current_provider == "ollama":
                return await self._chat_ollama(full_prompt, model)
            elif self.current_provider == "huggingface":
                return await self._chat_huggingface(full_prompt, model)
            elif self.current_provider == "groq":
                return await self._chat_groq(full_prompt, model)
            else:
                return await self._chat_fallback(full_prompt, model)
                
        except Exception as e:
            logger.error(f"Error in chat: {e}")
            return {"content": f"Error: {str(e)}", "usage": None}
    
    async def analyze_code(self, code: str, language: str, analysis_type: str, structure_analysis: Dict) -> Dict[str, Any]:
        """Analyze code with AI"""
        try:
            prompt = self._prepare_analysis_prompt(code, language, analysis_type, structure_analysis)
            
            response = await self.chat(prompt)
            
            # Parse the response to extract result and suggestions
            content = response.get("content", "")
            
            # Try to extract structured information
            result, suggestions = self._parse_analysis_response(content, analysis_type)
            
            return {
                "result": result,
                "suggestions": suggestions,
                "usage": response.get("usage")
            }
            
        except Exception as e:
            logger.error(f"Error in code analysis: {e}")
            return {"result": f"Error: {str(e)}", "suggestions": []}
    
    async def get_completions(self, context: str, language: str) -> List[str]:
        """Get code completions"""
        try:
            prompt = self._prepare_completion_prompt(context, language)
            
            response = await self.chat(prompt)
            content = response.get("content", "")
            
            # Parse completions from response
            completions = self._parse_completions(content)
            
            return completions[:5]  # Return top 5 completions
            
        except Exception as e:
            logger.error(f"Error getting completions: {e}")
            return []
    
    def _prepare_chat_prompt(self, prompt: str, context: Optional[str] = None) -> str:
        """Prepare chat prompt with context"""
        if context:
            return f"""Context:
{context}

User: {prompt}

Please provide a helpful response based on the context and user's request."""
        return prompt
    
    def _prepare_analysis_prompt(self, code: str, language: str, analysis_type: str, structure_analysis: Dict) -> str:
        """Prepare code analysis prompt"""
        prompts = {
            "explain": f"""Explain the following {language} code in detail. Include what it does, how it works, and any important concepts:

Code structure analysis:
{json.dumps(structure_analysis, indent=2)}

Code:
```{language}
{code}
```

Provide a clear, comprehensive explanation.""",

            "refactor": f"""Refactor the following {language} code to improve readability, performance, and maintainability. 

Current code structure:
{json.dumps(structure_analysis, indent=2)}

Code to refactor:
```{language}
{code}
```

Provide the refactored code and explain the improvements made.""",

            "test": f"""Generate comprehensive unit tests for the following {language} code. Include edge cases and error handling.

Code structure:
{json.dumps(structure_analysis, indent=2)}

Code to test:
```{language}
{code}
```

Generate complete test cases with assertions.""",

            "fix": f"""Analyze the following {language} code for bugs and provide fixes.

Code structure:
{json.dumps(structure_analysis, indent=2)}

Code to analyze:
```{language}
{code}
```

Identify potential bugs and provide corrected code.""",

            "docs": f"""Generate comprehensive documentation for the following {language} code.

Code structure:
{json.dumps(structure_analysis, indent=2)}

Code to document:
```{language}
{code}
```

Generate docstrings, parameter descriptions, return values, and usage examples."""
        }
        
        return prompts.get(analysis_type, prompts["explain"])
    
    def _prepare_completion_prompt(self, context: str, language: str) -> str:
        """Prepare code completion prompt"""
        return f"""Given the following {language} code context, suggest the next few lines of code that would logically follow. Provide multiple suggestions:

Context:
```{language}
{context}
```

Provide 3-5 different completion suggestions, each on a new line."""
    
    async def _chat_openai(self, prompt: str, model: str) -> Dict[str, Any]:
        """Chat with OpenAI"""
        try:
            response = await self.openai_client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "system", "content": "You are an expert programming assistant."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=2000,
                temperature=0.7
            )
            
            return {
                "content": response.choices[0].message.content,
                "usage": {
                    "prompt_tokens": response.usage.prompt_tokens,
                    "completion_tokens": response.usage.completion_tokens,
                    "total_tokens": response.usage.total_tokens
                }
            }
            
        except Exception as e:
            logger.error(f"OpenAI API error: {e}")
            raise
    
    async def _chat_anthropic(self, prompt: str, model: str) -> Dict[str, Any]:
        """Chat with Anthropic Claude"""
        try:
            response = await self.anthropic_client.messages.create(
                model=model,
                max_tokens=2000,
                messages=[
                    {"role": "user", "content": prompt}
                ]
            )
            
            return {
                "content": response.content[0].text,
                "usage": {
                    "prompt_tokens": response.usage.input_tokens,
                    "completion_tokens": response.usage.output_tokens,
                    "total_tokens": response.usage.input_tokens + response.usage.output_tokens
                }
            }
            
        except Exception as e:
            logger.error(f"Anthropic API error: {e}")
            raise
    
    async def _chat_ollama(self, prompt: str, model: str) -> Dict[str, Any]:
        """Chat with Ollama local model"""
        try:
            async with aiohttp.ClientSession() as session:
                payload = {
                    "model": model or self.current_model,
                    "prompt": prompt,
                    "stream": False
                }

                async with session.post(f"{self.ollama_url}/api/generate", json=payload) as response:
                    if response.status == 200:
                        result = await response.json()
                        return {
                            "content": result.get("response", "No response from Ollama"),
                            "usage": {
                                "prompt_tokens": result.get("prompt_eval_count", 0),
                                "completion_tokens": result.get("eval_count", 0),
                                "total_tokens": result.get("prompt_eval_count", 0) + result.get("eval_count", 0)
                            }
                        }
                    else:
                        error_text = await response.text()
                        raise Exception(f"Ollama API error {response.status}: {error_text}")

        except Exception as e:
            logger.error(f"Ollama API error: {e}")
            return {
                "content": f"Ollama error: {str(e)}. Make sure Ollama is running and the model '{model}' is installed.",
                "usage": None
            }

    async def _chat_huggingface(self, prompt: str, model: str) -> Dict[str, Any]:
        """Chat with Hugging Face transformers"""
        try:
            if not self.hf_pipeline:
                self._initialize_huggingface()

            if not self.hf_pipeline:
                raise Exception("Hugging Face pipeline not available")

            # Generate response
            response = self.hf_pipeline(
                prompt,
                max_length=len(prompt.split()) + 100,
                num_return_sequences=1,
                temperature=0.7,
                do_sample=True,
                pad_token_id=self.hf_pipeline.tokenizer.eos_token_id
            )

            generated_text = response[0]['generated_text']
            # Extract only the new part (remove the prompt)
            new_content = generated_text[len(prompt):].strip()

            return {
                "content": new_content or "No response generated",
                "usage": {
                    "prompt_tokens": len(prompt.split()),
                    "completion_tokens": len(new_content.split()),
                    "total_tokens": len(prompt.split()) + len(new_content.split())
                }
            }

        except Exception as e:
            logger.error(f"Hugging Face error: {e}")
            return {
                "content": f"Hugging Face error: {str(e)}",
                "usage": None
            }

    async def _chat_groq(self, prompt: str, model: str) -> Dict[str, Any]:
        """Chat with Groq (free tier available)"""
        try:
            groq_api_key = os.getenv("GROQ_API_KEY")
            if not groq_api_key:
                raise Exception("GROQ_API_KEY not configured")

            async with aiohttp.ClientSession() as session:
                headers = {
                    "Authorization": f"Bearer {groq_api_key}",
                    "Content-Type": "application/json"
                }

                payload = {
                    "messages": [
                        {"role": "system", "content": "You are an expert programming assistant."},
                        {"role": "user", "content": prompt}
                    ],
                    "model": model or "mixtral-8x7b-32768",
                    "temperature": 0.7,
                    "max_tokens": 1000
                }

                async with session.post("https://api.groq.com/openai/v1/chat/completions",
                                      headers=headers, json=payload) as response:
                    if response.status == 200:
                        result = await response.json()
                        return {
                            "content": result["choices"][0]["message"]["content"],
                            "usage": result.get("usage", {})
                        }
                    else:
                        error_text = await response.text()
                        raise Exception(f"Groq API error {response.status}: {error_text}")

        except Exception as e:
            logger.error(f"Groq API error: {e}")
            return {
                "content": f"Groq error: {str(e)}",
                "usage": None
            }

    async def _chat_fallback(self, prompt: str, model: str) -> Dict[str, Any]:
        """Fallback response when no AI provider is available"""
        return {
            "content": """I'm a code assistant, but no AI provider is currently configured.

To enable AI features, please configure one of these free options:

1. **Ollama (Recommended for local use)**:
   - Install Ollama from https://ollama.ai/
   - Run: `ollama pull codellama:7b`
   - Set AI_PROVIDER=ollama in .env

2. **Groq (Free API)**:
   - Get free API key from https://console.groq.com/
   - Set GROQ_API_KEY in .env
   - Set AI_PROVIDER=groq

3. **Hugging Face (Local)**:
   - Install: `pip install transformers torch`
   - Set AI_PROVIDER=huggingface

For your question about: "{}"
I'd recommend setting up one of these providers for AI-powered assistance.""".format(prompt[:100] + "..." if len(prompt) > 100 else prompt),
            "usage": None
        }
    
    def _parse_analysis_response(self, content: str, analysis_type: str) -> tuple[str, List[str]]:
        """Parse analysis response to extract result and suggestions"""
        # Simple parsing - in a real implementation, you might use more sophisticated parsing
        lines = content.split('\n')
        
        suggestions = []
        result = content
        
        # Look for suggestion patterns
        for line in lines:
            if line.strip().startswith('- ') or line.strip().startswith('* '):
                suggestions.append(line.strip()[2:])
        
        return result, suggestions
    
    def _parse_completions(self, content: str) -> List[str]:
        """Parse completions from AI response"""
        lines = content.split('\n')
        completions = []
        
        for line in lines:
            line = line.strip()
            if line and not line.startswith('#') and not line.startswith('//'):
                completions.append(line)
        
        return completions
