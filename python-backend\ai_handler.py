"""
AI Handler for processing AI requests and managing different AI providers
"""

import asyncio
import logging
import os
from typing import Dict, List, Optional, Any
import json

import openai
import anthropic
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)


class AIHandler:
    """Handles AI model interactions and processing"""
    
    def __init__(self):
        self.openai_client = None
        self.anthropic_client = None
        self.current_provider = os.getenv("AI_PROVIDER", "openai")
        self.current_model = os.getenv("AI_MODEL", "gpt-4")
        
        self._initialize_clients()
    
    def _initialize_clients(self):
        """Initialize AI service clients"""
        try:
            # Initialize OpenAI client
            openai_api_key = os.getenv("OPENAI_API_KEY")
            if openai_api_key:
                self.openai_client = openai.AsyncOpenAI(api_key=openai_api_key)
                logger.info("OpenAI client initialized")
            
            # Initialize Anthropic client
            anthropic_api_key = os.getenv("ANTHROPIC_API_KEY")
            if anthropic_api_key:
                self.anthropic_client = anthropic.AsyncAnthropic(api_key=anthropic_api_key)
                logger.info("Anthropic client initialized")
                
        except Exception as e:
            logger.error(f"Error initializing AI clients: {e}")
    
    async def chat(self, prompt: str, model: Optional[str] = None, context: Optional[str] = None) -> Dict[str, Any]:
        """Handle chat requests"""
        try:
            model = model or self.current_model
            
            # Prepare the full prompt with context
            full_prompt = self._prepare_chat_prompt(prompt, context)
            
            if self.current_provider == "openai" and self.openai_client:
                return await self._chat_openai(full_prompt, model)
            elif self.current_provider == "anthropic" and self.anthropic_client:
                return await self._chat_anthropic(full_prompt, model)
            else:
                return await self._chat_local(full_prompt, model)
                
        except Exception as e:
            logger.error(f"Error in chat: {e}")
            return {"content": f"Error: {str(e)}", "usage": None}
    
    async def analyze_code(self, code: str, language: str, analysis_type: str, structure_analysis: Dict) -> Dict[str, Any]:
        """Analyze code with AI"""
        try:
            prompt = self._prepare_analysis_prompt(code, language, analysis_type, structure_analysis)
            
            response = await self.chat(prompt)
            
            # Parse the response to extract result and suggestions
            content = response.get("content", "")
            
            # Try to extract structured information
            result, suggestions = self._parse_analysis_response(content, analysis_type)
            
            return {
                "result": result,
                "suggestions": suggestions,
                "usage": response.get("usage")
            }
            
        except Exception as e:
            logger.error(f"Error in code analysis: {e}")
            return {"result": f"Error: {str(e)}", "suggestions": []}
    
    async def get_completions(self, context: str, language: str) -> List[str]:
        """Get code completions"""
        try:
            prompt = self._prepare_completion_prompt(context, language)
            
            response = await self.chat(prompt)
            content = response.get("content", "")
            
            # Parse completions from response
            completions = self._parse_completions(content)
            
            return completions[:5]  # Return top 5 completions
            
        except Exception as e:
            logger.error(f"Error getting completions: {e}")
            return []
    
    def _prepare_chat_prompt(self, prompt: str, context: Optional[str] = None) -> str:
        """Prepare chat prompt with context"""
        if context:
            return f"""Context:
{context}

User: {prompt}

Please provide a helpful response based on the context and user's request."""
        return prompt
    
    def _prepare_analysis_prompt(self, code: str, language: str, analysis_type: str, structure_analysis: Dict) -> str:
        """Prepare code analysis prompt"""
        prompts = {
            "explain": f"""Explain the following {language} code in detail. Include what it does, how it works, and any important concepts:

Code structure analysis:
{json.dumps(structure_analysis, indent=2)}

Code:
```{language}
{code}
```

Provide a clear, comprehensive explanation.""",

            "refactor": f"""Refactor the following {language} code to improve readability, performance, and maintainability. 

Current code structure:
{json.dumps(structure_analysis, indent=2)}

Code to refactor:
```{language}
{code}
```

Provide the refactored code and explain the improvements made.""",

            "test": f"""Generate comprehensive unit tests for the following {language} code. Include edge cases and error handling.

Code structure:
{json.dumps(structure_analysis, indent=2)}

Code to test:
```{language}
{code}
```

Generate complete test cases with assertions.""",

            "fix": f"""Analyze the following {language} code for bugs and provide fixes.

Code structure:
{json.dumps(structure_analysis, indent=2)}

Code to analyze:
```{language}
{code}
```

Identify potential bugs and provide corrected code.""",

            "docs": f"""Generate comprehensive documentation for the following {language} code.

Code structure:
{json.dumps(structure_analysis, indent=2)}

Code to document:
```{language}
{code}
```

Generate docstrings, parameter descriptions, return values, and usage examples."""
        }
        
        return prompts.get(analysis_type, prompts["explain"])
    
    def _prepare_completion_prompt(self, context: str, language: str) -> str:
        """Prepare code completion prompt"""
        return f"""Given the following {language} code context, suggest the next few lines of code that would logically follow. Provide multiple suggestions:

Context:
```{language}
{context}
```

Provide 3-5 different completion suggestions, each on a new line."""
    
    async def _chat_openai(self, prompt: str, model: str) -> Dict[str, Any]:
        """Chat with OpenAI"""
        try:
            response = await self.openai_client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "system", "content": "You are an expert programming assistant."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=2000,
                temperature=0.7
            )
            
            return {
                "content": response.choices[0].message.content,
                "usage": {
                    "prompt_tokens": response.usage.prompt_tokens,
                    "completion_tokens": response.usage.completion_tokens,
                    "total_tokens": response.usage.total_tokens
                }
            }
            
        except Exception as e:
            logger.error(f"OpenAI API error: {e}")
            raise
    
    async def _chat_anthropic(self, prompt: str, model: str) -> Dict[str, Any]:
        """Chat with Anthropic Claude"""
        try:
            response = await self.anthropic_client.messages.create(
                model=model,
                max_tokens=2000,
                messages=[
                    {"role": "user", "content": prompt}
                ]
            )
            
            return {
                "content": response.content[0].text,
                "usage": {
                    "prompt_tokens": response.usage.input_tokens,
                    "completion_tokens": response.usage.output_tokens,
                    "total_tokens": response.usage.input_tokens + response.usage.output_tokens
                }
            }
            
        except Exception as e:
            logger.error(f"Anthropic API error: {e}")
            raise
    
    async def _chat_local(self, prompt: str, model: str) -> Dict[str, Any]:
        """Chat with local model (placeholder for Ollama or similar)"""
        # This is a placeholder for local model integration
        # You can integrate with Ollama, Hugging Face Transformers, etc.
        return {
            "content": "Local model not implemented yet. Please configure OpenAI or Anthropic API keys.",
            "usage": None
        }
    
    def _parse_analysis_response(self, content: str, analysis_type: str) -> tuple[str, List[str]]:
        """Parse analysis response to extract result and suggestions"""
        # Simple parsing - in a real implementation, you might use more sophisticated parsing
        lines = content.split('\n')
        
        suggestions = []
        result = content
        
        # Look for suggestion patterns
        for line in lines:
            if line.strip().startswith('- ') or line.strip().startswith('* '):
                suggestions.append(line.strip()[2:])
        
        return result, suggestions
    
    def _parse_completions(self, content: str) -> List[str]:
        """Parse completions from AI response"""
        lines = content.split('\n')
        completions = []
        
        for line in lines:
            line = line.strip()
            if line and not line.startswith('#') and not line.startswith('//'):
                completions.append(line)
        
        return completions
