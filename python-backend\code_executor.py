"""
Code Executor for AI Code Assistant
Handles safe execution of code in different languages
"""

import asyncio
import subprocess
import tempfile
import os
import sys
import logging
from typing import Dict, Any, Optional
import json
import time

logger = logging.getLogger(__name__)


class CodeExecutor:
    """Handles code execution for different programming languages"""
    
    def __init__(self):
        self.supported_languages = {
            'python': self._execute_python,
            'javascript': self._execute_javascript,
            'java': self._execute_java,
            'cpp': self._execute_cpp,
            'c': self._execute_c,
            'go': self._execute_go,
            'rust': self._execute_rust,
            'csharp': self._execute_csharp
        }
        
        # Security settings
        self.timeout = 30  # seconds
        self.max_output_size = 10000  # characters
    
    async def execute_code(self, code: str, language: str, input_data: str = "") -> Dict[str, Any]:
        """
        Execute code in the specified language
        
        Args:
            code: The source code to execute
            language: Programming language
            input_data: Optional input data for the program
            
        Returns:
            Dict containing execution results
        """
        try:
            if language not in self.supported_languages:
                return {
                    "success": False,
                    "error": f"Language '{language}' is not supported",
                    "output": "",
                    "execution_time": 0
                }
            
            start_time = time.time()
            
            # Execute the code
            result = await self.supported_languages[language](code, input_data)
            
            execution_time = time.time() - start_time
            result["execution_time"] = round(execution_time, 3)
            
            return result
            
        except Exception as e:
            logger.error(f"Error executing {language} code: {e}")
            return {
                "success": False,
                "error": str(e),
                "output": "",
                "execution_time": 0
            }
    
    async def _execute_python(self, code: str, input_data: str = "") -> Dict[str, Any]:
        """Execute Python code"""
        try:
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(code)
                temp_file = f.name
            
            try:
                # Run Python code
                process = await asyncio.create_subprocess_exec(
                    sys.executable, temp_file,
                    stdin=asyncio.subprocess.PIPE,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(input=input_data.encode() if input_data else None),
                    timeout=self.timeout
                )
                
                output = stdout.decode('utf-8', errors='replace')
                error = stderr.decode('utf-8', errors='replace')
                
                if len(output) > self.max_output_size:
                    output = output[:self.max_output_size] + "\n... (output truncated)"
                
                return {
                    "success": process.returncode == 0,
                    "output": output,
                    "error": error if process.returncode != 0 else "",
                    "return_code": process.returncode
                }
                
            finally:
                os.unlink(temp_file)
                
        except asyncio.TimeoutError:
            return {
                "success": False,
                "output": "",
                "error": f"Execution timed out after {self.timeout} seconds"
            }
        except Exception as e:
            return {
                "success": False,
                "output": "",
                "error": str(e)
            }
    
    async def _execute_javascript(self, code: str, input_data: str = "") -> Dict[str, Any]:
        """Execute JavaScript code using Node.js"""
        try:
            with tempfile.NamedTemporaryFile(mode='w', suffix='.js', delete=False) as f:
                f.write(code)
                temp_file = f.name
            
            try:
                # Check if Node.js is available
                process = await asyncio.create_subprocess_exec(
                    'node', '--version',
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                await process.communicate()
                
                if process.returncode != 0:
                    return {
                        "success": False,
                        "output": "",
                        "error": "Node.js is not installed or not available in PATH"
                    }
                
                # Run JavaScript code
                process = await asyncio.create_subprocess_exec(
                    'node', temp_file,
                    stdin=asyncio.subprocess.PIPE,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(input=input_data.encode() if input_data else None),
                    timeout=self.timeout
                )
                
                output = stdout.decode('utf-8', errors='replace')
                error = stderr.decode('utf-8', errors='replace')
                
                return {
                    "success": process.returncode == 0,
                    "output": output,
                    "error": error if process.returncode != 0 else "",
                    "return_code": process.returncode
                }
                
            finally:
                os.unlink(temp_file)
                
        except asyncio.TimeoutError:
            return {
                "success": False,
                "output": "",
                "error": f"Execution timed out after {self.timeout} seconds"
            }
        except Exception as e:
            return {
                "success": False,
                "output": "",
                "error": str(e)
            }
    
    async def _execute_java(self, code: str, input_data: str = "") -> Dict[str, Any]:
        """Execute Java code"""
        try:
            # Extract class name from code
            import re
            class_match = re.search(r'public\s+class\s+(\w+)', code)
            if not class_match:
                return {
                    "success": False,
                    "output": "",
                    "error": "No public class found in Java code"
                }
            
            class_name = class_match.group(1)
            
            with tempfile.TemporaryDirectory() as temp_dir:
                java_file = os.path.join(temp_dir, f"{class_name}.java")
                
                with open(java_file, 'w') as f:
                    f.write(code)
                
                # Compile Java code
                compile_process = await asyncio.create_subprocess_exec(
                    'javac', java_file,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                    cwd=temp_dir
                )
                
                compile_stdout, compile_stderr = await compile_process.communicate()
                
                if compile_process.returncode != 0:
                    return {
                        "success": False,
                        "output": "",
                        "error": f"Compilation error: {compile_stderr.decode('utf-8', errors='replace')}"
                    }
                
                # Run Java code
                run_process = await asyncio.create_subprocess_exec(
                    'java', class_name,
                    stdin=asyncio.subprocess.PIPE,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                    cwd=temp_dir
                )
                
                stdout, stderr = await asyncio.wait_for(
                    run_process.communicate(input=input_data.encode() if input_data else None),
                    timeout=self.timeout
                )
                
                output = stdout.decode('utf-8', errors='replace')
                error = stderr.decode('utf-8', errors='replace')
                
                return {
                    "success": run_process.returncode == 0,
                    "output": output,
                    "error": error if run_process.returncode != 0 else "",
                    "return_code": run_process.returncode
                }
                
        except asyncio.TimeoutError:
            return {
                "success": False,
                "output": "",
                "error": f"Execution timed out after {self.timeout} seconds"
            }
        except Exception as e:
            return {
                "success": False,
                "output": "",
                "error": str(e)
            }
    
    async def _execute_cpp(self, code: str, input_data: str = "") -> Dict[str, Any]:
        """Execute C++ code"""
        return await self._execute_compiled_language(code, input_data, 'cpp', 'g++', ['-o'])
    
    async def _execute_c(self, code: str, input_data: str = "") -> Dict[str, Any]:
        """Execute C code"""
        return await self._execute_compiled_language(code, input_data, 'c', 'gcc', ['-o'])
    
    async def _execute_go(self, code: str, input_data: str = "") -> Dict[str, Any]:
        """Execute Go code"""
        try:
            with tempfile.NamedTemporaryFile(mode='w', suffix='.go', delete=False) as f:
                f.write(code)
                temp_file = f.name
            
            try:
                # Run Go code directly
                process = await asyncio.create_subprocess_exec(
                    'go', 'run', temp_file,
                    stdin=asyncio.subprocess.PIPE,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(input=input_data.encode() if input_data else None),
                    timeout=self.timeout
                )
                
                output = stdout.decode('utf-8', errors='replace')
                error = stderr.decode('utf-8', errors='replace')
                
                return {
                    "success": process.returncode == 0,
                    "output": output,
                    "error": error if process.returncode != 0 else "",
                    "return_code": process.returncode
                }
                
            finally:
                os.unlink(temp_file)
                
        except Exception as e:
            return {
                "success": False,
                "output": "",
                "error": str(e)
            }
    
    async def _execute_rust(self, code: str, input_data: str = "") -> Dict[str, Any]:
        """Execute Rust code"""
        try:
            with tempfile.NamedTemporaryFile(mode='w', suffix='.rs', delete=False) as f:
                f.write(code)
                temp_file = f.name
            
            try:
                # Compile and run Rust code
                process = await asyncio.create_subprocess_exec(
                    'rustc', '--edition', '2021', '-o', temp_file + '.exe', temp_file,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                compile_stdout, compile_stderr = await process.communicate()
                
                if process.returncode != 0:
                    return {
                        "success": False,
                        "output": "",
                        "error": f"Compilation error: {compile_stderr.decode('utf-8', errors='replace')}"
                    }
                
                # Run the compiled executable
                run_process = await asyncio.create_subprocess_exec(
                    temp_file + '.exe',
                    stdin=asyncio.subprocess.PIPE,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                stdout, stderr = await asyncio.wait_for(
                    run_process.communicate(input=input_data.encode() if input_data else None),
                    timeout=self.timeout
                )
                
                output = stdout.decode('utf-8', errors='replace')
                error = stderr.decode('utf-8', errors='replace')
                
                return {
                    "success": run_process.returncode == 0,
                    "output": output,
                    "error": error if run_process.returncode != 0 else "",
                    "return_code": run_process.returncode
                }
                
            finally:
                os.unlink(temp_file)
                if os.path.exists(temp_file + '.exe'):
                    os.unlink(temp_file + '.exe')
                
        except Exception as e:
            return {
                "success": False,
                "output": "",
                "error": str(e)
            }
    
    async def _execute_csharp(self, code: str, input_data: str = "") -> Dict[str, Any]:
        """Execute C# code"""
        try:
            with tempfile.NamedTemporaryFile(mode='w', suffix='.cs', delete=False) as f:
                f.write(code)
                temp_file = f.name
            
            try:
                # Compile C# code
                process = await asyncio.create_subprocess_exec(
                    'csc', '/out:' + temp_file + '.exe', temp_file,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                compile_stdout, compile_stderr = await process.communicate()
                
                if process.returncode != 0:
                    return {
                        "success": False,
                        "output": "",
                        "error": f"Compilation error: {compile_stderr.decode('utf-8', errors='replace')}"
                    }
                
                # Run the compiled executable
                run_process = await asyncio.create_subprocess_exec(
                    temp_file + '.exe',
                    stdin=asyncio.subprocess.PIPE,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                stdout, stderr = await asyncio.wait_for(
                    run_process.communicate(input=input_data.encode() if input_data else None),
                    timeout=self.timeout
                )
                
                output = stdout.decode('utf-8', errors='replace')
                error = stderr.decode('utf-8', errors='replace')
                
                return {
                    "success": run_process.returncode == 0,
                    "output": output,
                    "error": error if run_process.returncode != 0 else "",
                    "return_code": run_process.returncode
                }
                
            finally:
                os.unlink(temp_file)
                if os.path.exists(temp_file + '.exe'):
                    os.unlink(temp_file + '.exe')
                
        except Exception as e:
            return {
                "success": False,
                "output": "",
                "error": str(e)
            }
    
    async def _execute_compiled_language(self, code: str, input_data: str, extension: str, 
                                       compiler: str, compile_args: list) -> Dict[str, Any]:
        """Generic method for compiled languages"""
        try:
            with tempfile.NamedTemporaryFile(mode='w', suffix=f'.{extension}', delete=False) as f:
                f.write(code)
                temp_file = f.name
            
            executable = temp_file + '.exe'
            
            try:
                # Compile
                compile_cmd = [compiler] + compile_args + [executable, temp_file]
                process = await asyncio.create_subprocess_exec(
                    *compile_cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                compile_stdout, compile_stderr = await process.communicate()
                
                if process.returncode != 0:
                    return {
                        "success": False,
                        "output": "",
                        "error": f"Compilation error: {compile_stderr.decode('utf-8', errors='replace')}"
                    }
                
                # Run
                run_process = await asyncio.create_subprocess_exec(
                    executable,
                    stdin=asyncio.subprocess.PIPE,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                stdout, stderr = await asyncio.wait_for(
                    run_process.communicate(input=input_data.encode() if input_data else None),
                    timeout=self.timeout
                )
                
                output = stdout.decode('utf-8', errors='replace')
                error = stderr.decode('utf-8', errors='replace')
                
                return {
                    "success": run_process.returncode == 0,
                    "output": output,
                    "error": error if run_process.returncode != 0 else "",
                    "return_code": run_process.returncode
                }
                
            finally:
                os.unlink(temp_file)
                if os.path.exists(executable):
                    os.unlink(executable)
                
        except Exception as e:
            return {
                "success": False,
                "output": "",
                "error": str(e)
            }
