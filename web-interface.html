<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Code Assistant - Web Interface</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
        }

        .section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            border: 1px solid #e9ecef;
        }

        .section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.5em;
            border-bottom: 2px solid #4facfe;
            padding-bottom: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }

        input, textarea, select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #4facfe;
        }

        textarea {
            resize: vertical;
            min-height: 120px;
            font-family: 'Courier New', monospace;
        }

        button {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        }

        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .result {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            max-height: 400px;
            overflow-y: auto;
        }

        .loading {
            text-align: center;
            color: #666;
            font-style: italic;
        }

        .error {
            color: #dc3545;
            background: #f8d7da;
            border-color: #f5c6cb;
        }

        .success {
            color: #155724;
            background: #d4edda;
            border-color: #c3e6cb;
        }

        .status {
            text-align: center;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 8px;
            font-weight: 600;
        }

        .features {
            grid-column: 1 / -1;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0 -30px -30px -30px;
            padding: 30px;
        }

        .features h2 {
            color: white;
            border-bottom-color: white;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .feature {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }

        .feature h3 {
            margin-bottom: 10px;
            font-size: 1.2em;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AI Code Assistant</h1>
            <p>Powered by Free Groq API - Your AI Programming Companion</p>
        </div>

        <div id="status" class="status"></div>

        <div class="main-content">
            <div class="section">
                <h2>💬 AI Chat</h2>
                <div class="form-group">
                    <label for="chatPrompt">Ask the AI anything about programming:</label>
                    <textarea id="chatPrompt" placeholder="e.g., How do I create a REST API in Python?"></textarea>
                </div>
                <button onclick="chatWithAI()">Ask AI</button>
                <div id="chatResult" class="result" style="display: none;"></div>
            </div>

            <div class="section">
                <h2>🔍 Code Analysis</h2>
                <div class="form-group">
                    <label for="codeInput">Paste your code here:</label>
                    <textarea id="codeInput" placeholder="def hello_world():&#10;    print('Hello, World!')"></textarea>
                </div>
                <div class="form-group">
                    <label for="language">Language:</label>
                    <select id="language">
                        <option value="python">Python</option>
                        <option value="javascript">JavaScript</option>
                        <option value="java">Java</option>
                        <option value="cpp">C++</option>
                        <option value="csharp">C#</option>
                        <option value="go">Go</option>
                        <option value="rust">Rust</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="analysisType">Analysis Type:</label>
                    <select id="analysisType">
                        <option value="explain">Explain Code</option>
                        <option value="refactor">Refactor Code</option>
                        <option value="test">Generate Tests</option>
                        <option value="fix">Fix Bugs</option>
                        <option value="docs">Generate Documentation</option>
                    </select>
                </div>
                <button onclick="analyzeCode()">Analyze Code</button>
                <div id="analysisResult" class="result" style="display: none;"></div>
            </div>

            <div class="features">
                <h2>✨ Features</h2>
                <div class="feature-grid">
                    <div class="feature">
                        <h3>🤖 AI Chat</h3>
                        <p>Ask questions about programming, get explanations, and receive coding guidance from AI.</p>
                    </div>
                    <div class="feature">
                        <h3>🔍 Code Analysis</h3>
                        <p>Understand what your code does with detailed explanations and complexity analysis.</p>
                    </div>
                    <div class="feature">
                        <h3>🛠️ Code Refactoring</h3>
                        <p>Improve code quality with AI-suggested refactoring and optimization.</p>
                    </div>
                    <div class="feature">
                        <h3>🧪 Test Generation</h3>
                        <p>Automatically generate comprehensive unit tests with edge cases.</p>
                    </div>
                    <div class="feature">
                        <h3>🐛 Bug Detection</h3>
                        <p>Identify and fix potential bugs in your code automatically.</p>
                    </div>
                    <div class="feature">
                        <h3>📝 Documentation</h3>
                        <p>Generate professional documentation and docstrings for your functions.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';

        // Check server status on load
        window.onload = checkServerStatus;

        async function checkServerStatus() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                if (response.ok) {
                    showStatus('✅ AI Backend Server is running and ready!', 'success');
                } else {
                    showStatus('⚠️ Server responded but may have issues', 'error');
                }
            } catch (error) {
                showStatus('❌ Cannot connect to AI backend. Make sure the server is running on http://localhost:8000', 'error');
            }
        }

        function showStatus(message, type) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        async function chatWithAI() {
            const prompt = document.getElementById('chatPrompt').value.trim();
            if (!prompt) {
                alert('Please enter a question or prompt');
                return;
            }

            const resultDiv = document.getElementById('chatResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = 'AI is thinking...';
            resultDiv.className = 'result loading';

            try {
                const response = await fetch(`${API_BASE}/ai/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        model: 'llama3-8b-8192'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    resultDiv.textContent = `🤖 AI Response:\n\n${data.response}`;
                    resultDiv.className = 'result success';
                } else {
                    throw new Error(`Server error: ${response.status}`);
                }
            } catch (error) {
                resultDiv.textContent = `❌ Error: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        async function analyzeCode() {
            const code = document.getElementById('codeInput').value.trim();
            const language = document.getElementById('language').value;
            const analysisType = document.getElementById('analysisType').value;

            if (!code) {
                alert('Please enter some code to analyze');
                return;
            }

            const resultDiv = document.getElementById('analysisResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = `AI is analyzing your ${language} code...`;
            resultDiv.className = 'result loading';

            try {
                const response = await fetch(`${API_BASE}/ai/analyze`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        code: code,
                        language: language,
                        analysis_type: analysisType
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    let result = `🔍 ${analysisType.charAt(0).toUpperCase() + analysisType.slice(1)} Analysis:\n\n${data.result}`;
                    
                    if (data.suggestions && data.suggestions.length > 0) {
                        result += '\n\n💡 Suggestions:\n';
                        data.suggestions.forEach((suggestion, index) => {
                            result += `${index + 1}. ${suggestion}\n`;
                        });
                    }
                    
                    resultDiv.textContent = result;
                    resultDiv.className = 'result success';
                } else {
                    throw new Error(`Server error: ${response.status}`);
                }
            } catch (error) {
                resultDiv.textContent = `❌ Error: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // Add Enter key support for chat
        document.getElementById('chatPrompt').addEventListener('keydown', function(event) {
            if (event.ctrlKey && event.key === 'Enter') {
                chatWithAI();
            }
        });
    </script>
</body>
</html>
