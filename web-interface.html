<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Code Assistant - Web Interface</title>
    <!-- Include Prism.js for syntax highlighting -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/line-numbers/prism-line-numbers.min.css" rel="stylesheet" />
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
        }

        .section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            border: 1px solid #e9ecef;
        }

        .section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.5em;
            border-bottom: 2px solid #4facfe;
            padding-bottom: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }

        input, textarea, select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #4facfe;
        }

        textarea {
            resize: vertical;
            min-height: 120px;
            font-family: 'Fira Code', 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
            background: #2d3748;
            color: #e2e8f0;
            border: 2px solid #4a5568;
        }

        textarea:focus {
            background: #2d3748;
            color: #e2e8f0;
            border-color: #4facfe;
        }

        .code-editor {
            position: relative;
            background: #1a202c;
            border-radius: 8px;
            overflow: hidden;
            border: 2px solid #4a5568;
        }

        .code-editor-header {
            background: #2d3748;
            padding: 10px 15px;
            border-bottom: 1px solid #4a5568;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .code-editor-header .dots {
            display: flex;
            gap: 5px;
        }

        .code-editor-header .dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .dot.red { background: #ff5f56; }
        .dot.yellow { background: #ffbd2e; }
        .dot.green { background: #27ca3f; }

        .code-editor-header .filename {
            color: #a0aec0;
            font-size: 14px;
            font-family: 'Fira Code', monospace;
        }

        .code-display {
            background: #1a202c;
            color: #e2e8f0;
            padding: 0;
            margin: 0;
            font-family: 'Fira Code', 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
            overflow-x: auto;
            max-height: 500px;
            overflow-y: auto;
        }

        .code-display pre {
            margin: 0;
            padding: 20px;
            background: transparent;
        }

        .code-display code {
            background: transparent;
            color: inherit;
            font-size: inherit;
            font-family: inherit;
        }

        button {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        }

        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .result {
            background: #1a202c;
            border: 2px solid #4a5568;
            border-radius: 8px;
            padding: 0;
            margin-top: 20px;
            max-height: 500px;
            overflow: hidden;
        }

        .result-header {
            background: #2d3748;
            padding: 10px 15px;
            border-bottom: 1px solid #4a5568;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .result-title {
            color: #e2e8f0;
            font-weight: 600;
            font-size: 14px;
        }

        .copy-btn {
            background: #4a5568;
            color: #e2e8f0;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: background 0.2s;
        }

        .copy-btn:hover {
            background: #718096;
        }

        .result-content {
            padding: 20px;
            color: #e2e8f0;
            font-family: 'Fira Code', 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
            white-space: pre-wrap;
            overflow-y: auto;
            max-height: 450px;
        }

        .loading {
            text-align: center;
            color: #666;
            font-style: italic;
        }

        .error {
            color: #dc3545;
            background: #f8d7da;
            border-color: #f5c6cb;
        }

        .success {
            color: #155724;
            background: #d4edda;
            border-color: #c3e6cb;
        }

        .status {
            text-align: center;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 8px;
            font-weight: 600;
        }

        .features {
            grid-column: 1 / -1;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0 -30px -30px -30px;
            padding: 30px;
        }

        .features h2 {
            color: white;
            border-bottom-color: white;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .feature {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }

        .feature h3 {
            margin-bottom: 10px;
            font-size: 1.2em;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AI Code Assistant</h1>
            <p>Powered by Free Groq API - Your AI Programming Companion</p>
        </div>

        <div id="status" class="status"></div>

        <div class="main-content">
            <div class="section">
                <h2>💬 AI Chat</h2>
                <div class="form-group">
                    <label for="chatPrompt">Ask the AI anything about programming:</label>
                    <textarea id="chatPrompt" placeholder="e.g., How do I create a REST API in Python?"></textarea>
                </div>
                <button onclick="chatWithAI()">Ask AI</button>
                <div id="chatResult" class="result" style="display: none;">
                    <div class="result-header">
                        <div class="result-title">🤖 AI Response</div>
                        <button class="copy-btn" onclick="copyToClipboard('chatResult')">Copy</button>
                    </div>
                    <div class="result-content"></div>
                </div>
            </div>

            <div class="section">
                <h2>🔍 Code Analysis</h2>
                <div class="form-group">
                    <label for="codeInput">Paste your code here:</label>
                    <div class="code-editor">
                        <div class="code-editor-header">
                            <div class="dots">
                                <div class="dot red"></div>
                                <div class="dot yellow"></div>
                                <div class="dot green"></div>
                            </div>
                            <div class="filename">main.<span id="fileExtension">py</span></div>
                        </div>
                        <textarea id="codeInput" placeholder="def hello_world():&#10;    print('Hello, World!')"></textarea>
                    </div>
                </div>
                <div class="form-group">
                    <label for="language">Language:</label>
                    <select id="language">
                        <option value="python">Python</option>
                        <option value="javascript">JavaScript</option>
                        <option value="java">Java</option>
                        <option value="cpp">C++</option>
                        <option value="csharp">C#</option>
                        <option value="go">Go</option>
                        <option value="rust">Rust</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="analysisType">Analysis Type:</label>
                    <select id="analysisType">
                        <option value="explain">Explain Code</option>
                        <option value="refactor">Refactor Code</option>
                        <option value="test">Generate Tests</option>
                        <option value="fix">Fix Bugs</option>
                        <option value="docs">Generate Documentation</option>
                    </select>
                </div>
                <button onclick="analyzeCode()">Analyze Code</button>
                <div id="analysisResult" class="result" style="display: none;">
                    <div class="result-header">
                        <div class="result-title">🔍 Analysis Result</div>
                        <button class="copy-btn" onclick="copyToClipboard('analysisResult')">Copy</button>
                    </div>
                    <div class="result-content"></div>
                </div>
            </div>

            <div class="features">
                <h2>✨ Features</h2>
                <div class="feature-grid">
                    <div class="feature">
                        <h3>🤖 AI Chat</h3>
                        <p>Ask questions about programming, get explanations, and receive coding guidance from AI.</p>
                    </div>
                    <div class="feature">
                        <h3>🔍 Code Analysis</h3>
                        <p>Understand what your code does with detailed explanations and complexity analysis.</p>
                    </div>
                    <div class="feature">
                        <h3>🛠️ Code Refactoring</h3>
                        <p>Improve code quality with AI-suggested refactoring and optimization.</p>
                    </div>
                    <div class="feature">
                        <h3>🧪 Test Generation</h3>
                        <p>Automatically generate comprehensive unit tests with edge cases.</p>
                    </div>
                    <div class="feature">
                        <h3>🐛 Bug Detection</h3>
                        <p>Identify and fix potential bugs in your code automatically.</p>
                    </div>
                    <div class="feature">
                        <h3>📝 Documentation</h3>
                        <p>Generate professional documentation and docstrings for your functions.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Include Prism.js for syntax highlighting -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/line-numbers/prism-line-numbers.min.js"></script>

    <script>
        const API_BASE = 'http://localhost:8000';

        // Check server status on load
        window.onload = function() {
            checkServerStatus();
            updateFileExtension();
        };

        async function checkServerStatus() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                if (response.ok) {
                    showStatus('✅ AI Backend Server is running and ready!', 'success');
                } else {
                    showStatus('⚠️ Server responded but may have issues', 'error');
                }
            } catch (error) {
                showStatus('❌ Cannot connect to AI backend. Make sure the server is running on http://localhost:8000', 'error');
            }
        }

        function showStatus(message, type) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        async function chatWithAI() {
            const prompt = document.getElementById('chatPrompt').value.trim();
            if (!prompt) {
                alert('Please enter a question or prompt');
                return;
            }

            const resultDiv = document.getElementById('chatResult');
            const contentDiv = resultDiv.querySelector('.result-content');
            resultDiv.style.display = 'block';
            contentDiv.textContent = 'AI is thinking...';
            contentDiv.className = 'result-content loading';

            try {
                const response = await fetch(`${API_BASE}/ai/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        model: 'llama3-8b-8192'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    displayFormattedResponse(contentDiv, data.response, 'text');
                    contentDiv.className = 'result-content success';
                } else {
                    throw new Error(`Server error: ${response.status}`);
                }
            } catch (error) {
                contentDiv.textContent = `❌ Error: ${error.message}`;
                contentDiv.className = 'result-content error';
            }
        }

        async function analyzeCode() {
            const code = document.getElementById('codeInput').value.trim();
            const language = document.getElementById('language').value;
            const analysisType = document.getElementById('analysisType').value;

            if (!code) {
                alert('Please enter some code to analyze');
                return;
            }

            const resultDiv = document.getElementById('analysisResult');
            const contentDiv = resultDiv.querySelector('.result-content');
            const titleDiv = resultDiv.querySelector('.result-title');

            resultDiv.style.display = 'block';
            titleDiv.textContent = `🔍 ${analysisType.charAt(0).toUpperCase() + analysisType.slice(1)} Analysis`;
            contentDiv.textContent = `AI is analyzing your ${language} code...`;
            contentDiv.className = 'result-content loading';

            try {
                const response = await fetch(`${API_BASE}/ai/analyze`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        code: code,
                        language: language,
                        analysis_type: analysisType
                    })
                });

                if (response.ok) {
                    const data = await response.json();

                    // Determine if the result contains code
                    const containsCode = analysisType === 'refactor' || analysisType === 'fix' || analysisType === 'test';
                    const resultLanguage = containsCode ? language : 'text';

                    displayFormattedResponse(contentDiv, data.result, resultLanguage, data.suggestions);
                    contentDiv.className = 'result-content success';
                } else {
                    throw new Error(`Server error: ${response.status}`);
                }
            } catch (error) {
                contentDiv.textContent = `❌ Error: ${error.message}`;
                contentDiv.className = 'result-content error';
            }
        }

        // Helper function to display formatted responses with syntax highlighting
        function displayFormattedResponse(container, content, language = 'text', suggestions = null) {
            container.innerHTML = '';

            // Extract code blocks from the content
            const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
            let lastIndex = 0;
            let match;

            while ((match = codeBlockRegex.exec(content)) !== null) {
                // Add text before code block
                if (match.index > lastIndex) {
                    const textContent = content.substring(lastIndex, match.index);
                    if (textContent.trim()) {
                        const textDiv = document.createElement('div');
                        textDiv.textContent = textContent;
                        textDiv.style.marginBottom = '15px';
                        container.appendChild(textDiv);
                    }
                }

                // Add code block with syntax highlighting
                const codeLanguage = match[1] || language;
                const codeContent = match[2];

                const codeBlock = createCodeBlock(codeContent, codeLanguage);
                container.appendChild(codeBlock);

                lastIndex = match.index + match[0].length;
            }

            // Add remaining text
            if (lastIndex < content.length) {
                const remainingText = content.substring(lastIndex);
                if (remainingText.trim()) {
                    const textDiv = document.createElement('div');
                    textDiv.textContent = remainingText;
                    container.appendChild(textDiv);
                }
            }

            // If no code blocks found, check if the entire content is code
            if (lastIndex === 0 && (language !== 'text' && language !== 'markdown')) {
                const codeBlock = createCodeBlock(content, language);
                container.appendChild(codeBlock);
            } else if (lastIndex === 0) {
                // Plain text content
                container.textContent = content;
            }

            // Add suggestions if provided
            if (suggestions && suggestions.length > 0) {
                const suggestionsDiv = document.createElement('div');
                suggestionsDiv.style.marginTop = '20px';
                suggestionsDiv.style.borderTop = '1px solid #4a5568';
                suggestionsDiv.style.paddingTop = '15px';

                const suggestionsTitle = document.createElement('div');
                suggestionsTitle.textContent = '💡 Suggestions:';
                suggestionsTitle.style.fontWeight = 'bold';
                suggestionsTitle.style.marginBottom = '10px';
                suggestionsDiv.appendChild(suggestionsTitle);

                suggestions.forEach((suggestion, index) => {
                    const suggestionDiv = document.createElement('div');
                    suggestionDiv.textContent = `${index + 1}. ${suggestion}`;
                    suggestionDiv.style.marginBottom = '5px';
                    suggestionsDiv.appendChild(suggestionDiv);
                });

                container.appendChild(suggestionsDiv);
            }
        }

        // Helper function to create a code block with syntax highlighting
        function createCodeBlock(code, language) {
            const codeContainer = document.createElement('div');
            codeContainer.className = 'code-editor';
            codeContainer.style.marginBottom = '15px';

            // Header
            const header = document.createElement('div');
            header.className = 'code-editor-header';
            header.innerHTML = `
                <div class="dots">
                    <div class="dot red"></div>
                    <div class="dot yellow"></div>
                    <div class="dot green"></div>
                </div>
                <div class="filename">result.${getFileExtension(language)}</div>
            `;
            codeContainer.appendChild(header);

            // Code content
            const codeDisplay = document.createElement('div');
            codeDisplay.className = 'code-display';

            const pre = document.createElement('pre');
            pre.className = 'line-numbers';

            const codeElement = document.createElement('code');
            codeElement.className = `language-${language}`;
            codeElement.textContent = code;

            pre.appendChild(codeElement);
            codeDisplay.appendChild(pre);
            codeContainer.appendChild(codeDisplay);

            // Apply syntax highlighting
            setTimeout(() => {
                if (window.Prism) {
                    Prism.highlightElement(codeElement);
                }
            }, 100);

            return codeContainer;
        }

        // Helper function to get file extension based on language
        function getFileExtension(language) {
            const extensions = {
                'python': 'py',
                'javascript': 'js',
                'typescript': 'ts',
                'java': 'java',
                'cpp': 'cpp',
                'c': 'c',
                'csharp': 'cs',
                'go': 'go',
                'rust': 'rs',
                'php': 'php',
                'ruby': 'rb',
                'html': 'html',
                'css': 'css',
                'sql': 'sql',
                'json': 'json',
                'xml': 'xml',
                'yaml': 'yml',
                'markdown': 'md'
            };
            return extensions[language] || 'txt';
        }

        // Update file extension in code editor header
        function updateFileExtension() {
            const language = document.getElementById('language').value;
            const extensionSpan = document.getElementById('fileExtension');
            if (extensionSpan) {
                extensionSpan.textContent = getFileExtension(language);
            }
        }

        // Copy to clipboard function
        function copyToClipboard(resultId) {
            const resultDiv = document.getElementById(resultId);
            const contentDiv = resultDiv.querySelector('.result-content');

            // Get text content, preserving code blocks
            let textToCopy = '';
            const codeBlocks = contentDiv.querySelectorAll('code');

            if (codeBlocks.length > 0) {
                // If there are code blocks, extract them properly
                codeBlocks.forEach(block => {
                    textToCopy += block.textContent + '\n\n';
                });
            } else {
                // Otherwise, get all text content
                textToCopy = contentDiv.textContent;
            }

            navigator.clipboard.writeText(textToCopy).then(() => {
                // Show feedback
                const copyBtn = resultDiv.querySelector('.copy-btn');
                const originalText = copyBtn.textContent;
                copyBtn.textContent = 'Copied!';
                setTimeout(() => {
                    copyBtn.textContent = originalText;
                }, 2000);
            }).catch(err => {
                console.error('Failed to copy: ', err);
                alert('Failed to copy to clipboard');
            });
        }

        // Add event listener for language change
        document.getElementById('language').addEventListener('change', updateFileExtension);

        // Add Enter key support for chat
        document.getElementById('chatPrompt').addEventListener('keydown', function(event) {
            if (event.ctrlKey && event.key === 'Enter') {
                chatWithAI();
            }
        });
    </script>
</body>
</html>
