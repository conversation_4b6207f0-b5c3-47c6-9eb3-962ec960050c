#!/usr/bin/env python3
"""
Test script for free AI models
Run this to verify your free AI setup is working
"""

import asyncio
import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path to import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ai_handler import AIHandler


async def test_ai_provider():
    """Test the configured AI provider"""
    print("🤖 Testing AI Code Assistant with Free Models")
    print("=" * 50)
    
    # Initialize AI handler
    ai_handler = AIHandler()
    
    provider = ai_handler.current_provider
    model = ai_handler.current_model
    
    print(f"Provider: {provider}")
    print(f"Model: {model}")
    print()
    
    # Test cases
    test_cases = [
        {
            "name": "Simple Chat",
            "prompt": "Hello! Can you help me with Python programming?",
            "expected_keywords": ["python", "help", "programming"]
        },
        {
            "name": "Code Explanation",
            "prompt": "Explain this Python code: def fibonacci(n): return n if n <= 1 else fibon<PERSON>ci(n-1) + fi<PERSON><PERSON><PERSON>(n-2)",
            "expected_keywords": ["fibonacci", "recursive", "function"]
        },
        {
            "name": "Code Completion",
            "prompt": "Complete this Python function: def calculate_average(numbers):",
            "expected_keywords": ["sum", "len", "return"]
        }
    ]
    
    print("🧪 Running Tests...")
    print("-" * 30)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest {i}: {test_case['name']}")
        print(f"Prompt: {test_case['prompt'][:50]}...")
        
        try:
            # Make AI request
            response = await ai_handler.chat(test_case['prompt'])
            
            if response and response.get('content'):
                content = response['content']
                print(f"✅ Response received ({len(content)} chars)")
                print(f"Preview: {content[:100]}...")
                
                # Check for expected keywords (basic validation)
                found_keywords = []
                for keyword in test_case['expected_keywords']:
                    if keyword.lower() in content.lower():
                        found_keywords.append(keyword)
                
                if found_keywords:
                    print(f"✅ Found relevant keywords: {found_keywords}")
                else:
                    print(f"⚠️  No expected keywords found, but response received")
                
                # Check usage info
                if response.get('usage'):
                    usage = response['usage']
                    print(f"📊 Tokens - Prompt: {usage.get('prompt_tokens', 'N/A')}, "
                          f"Completion: {usage.get('completion_tokens', 'N/A')}")
                
            else:
                print(f"❌ No response content received")
                print(f"Full response: {response}")
                
        except Exception as e:
            print(f"❌ Error: {str(e)}")
    
    print("\n" + "=" * 50)
    print("🎯 Test Summary")
    
    # Provider-specific tips
    if provider == "ollama":
        print("\n💡 Ollama Tips:")
        print("- Make sure Ollama is running: ollama serve")
        print("- Check available models: ollama list")
        print("- Pull models: ollama pull codellama:7b")
        
    elif provider == "groq":
        print("\n💡 Groq Tips:")
        print("- Check your API key at https://console.groq.com/")
        print("- Free tier has rate limits")
        print("- Try again in a few minutes if rate limited")
        
    elif provider == "huggingface":
        print("\n💡 Hugging Face Tips:")
        print("- First run downloads the model (be patient)")
        print("- Requires transformers and torch packages")
        print("- Use GPU for better performance")
    
    print("\n🚀 If tests passed, your AI assistant is ready!")


async def test_specific_features():
    """Test specific AI features"""
    print("\n🔧 Testing Specific Features...")
    print("-" * 30)
    
    ai_handler = AIHandler()
    
    # Test code analysis
    test_code = """
def bubble_sort(arr):
    n = len(arr)
    for i in range(n):
        for j in range(0, n-i-1):
            if arr[j] > arr[j+1]:
                arr[j], arr[j+1] = arr[j+1], arr[j]
    return arr
"""
    
    print("\n1. Testing Code Analysis...")
    try:
        analysis_response = await ai_handler.analyze_code(
            code=test_code,
            language="python",
            analysis_type="explain",
            structure_analysis={"functions": [{"name": "bubble_sort", "line": 1}]}
        )
        
        if analysis_response.get('result'):
            print("✅ Code analysis working")
            print(f"Preview: {analysis_response['result'][:100]}...")
        else:
            print("❌ Code analysis failed")
            
    except Exception as e:
        print(f"❌ Code analysis error: {e}")
    
    print("\n2. Testing Code Completion...")
    try:
        completions = await ai_handler.get_completions(
            context="def calculate_sum(a, b):\n    # Calculate the sum of two numbers\n    ",
            language="python"
        )
        
        if completions:
            print(f"✅ Code completion working ({len(completions)} suggestions)")
            for i, completion in enumerate(completions[:3], 1):
                print(f"  {i}. {completion[:50]}...")
        else:
            print("❌ No completions generated")
            
    except Exception as e:
        print(f"❌ Code completion error: {e}")


def check_environment():
    """Check environment configuration"""
    print("🔍 Checking Environment Configuration...")
    print("-" * 40)
    
    provider = os.getenv("AI_PROVIDER", "not set")
    model = os.getenv("AI_MODEL", "not set")
    
    print(f"AI_PROVIDER: {provider}")
    print(f"AI_MODEL: {model}")
    
    if provider == "ollama":
        ollama_url = os.getenv("OLLAMA_URL", "http://localhost:11434")
        print(f"OLLAMA_URL: {ollama_url}")
        
        # Test Ollama connection
        try:
            import requests
            response = requests.get(f"{ollama_url}/api/tags", timeout=5)
            if response.status_code == 200:
                models = response.json().get("models", [])
                print(f"✅ Ollama connected, {len(models)} models available")
                for model_info in models[:3]:
                    print(f"  - {model_info.get('name', 'Unknown')}")
            else:
                print(f"❌ Ollama connection failed: {response.status_code}")
        except Exception as e:
            print(f"❌ Cannot connect to Ollama: {e}")
            print("💡 Make sure Ollama is running: ollama serve")
    
    elif provider == "groq":
        api_key = os.getenv("GROQ_API_KEY", "not set")
        if api_key and api_key != "not set" and api_key != "your_groq_api_key_here":
            print(f"✅ Groq API key configured")
        else:
            print(f"❌ Groq API key not configured")
            print("💡 Get free key from https://console.groq.com/")
    
    elif provider == "huggingface":
        hf_model = os.getenv("HF_MODEL", "microsoft/DialoGPT-medium")
        print(f"HF_MODEL: {hf_model}")
        
        try:
            import transformers
            import torch
            print(f"✅ Transformers available (v{transformers.__version__})")
            print(f"✅ PyTorch available (v{torch.__version__})")
            if torch.cuda.is_available():
                print(f"✅ CUDA available ({torch.cuda.get_device_name(0)})")
            else:
                print(f"ℹ️  CUDA not available (using CPU)")
        except ImportError as e:
            print(f"❌ Missing dependencies: {e}")
            print("💡 Install with: pip install transformers torch")
    
    print()


async def main():
    """Main test function"""
    print("🧪 AI Code Assistant - Free Models Test Suite")
    print("=" * 60)
    
    # Check environment
    check_environment()
    
    # Test AI provider
    await test_ai_provider()
    
    # Test specific features
    await test_specific_features()
    
    print("\n" + "=" * 60)
    print("✨ Testing Complete!")
    print("\nIf all tests passed, you can now:")
    print("1. Start the backend: python main.py")
    print("2. Install the VS Code extension")
    print("3. Start coding with AI assistance!")


if __name__ == "__main__":
    asyncio.run(main())
