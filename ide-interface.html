<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Code Assistant - Full IDE</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/line-numbers/prism-line-numbers.min.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" />
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1e1e1e;
            color: #d4d4d4;
            height: 100vh;
            overflow: hidden;
        }

        .ide-container {
            display: grid;
            grid-template-areas: 
                "titlebar titlebar titlebar"
                "sidebar editor-area terminal";
            grid-template-columns: 250px 1fr 400px;
            grid-template-rows: 35px 1fr;
            height: 100vh;
        }

        .titlebar {
            grid-area: titlebar;
            background: #323233;
            display: flex;
            align-items: center;
            padding: 0 15px;
            border-bottom: 1px solid #2d2d30;
        }

        .titlebar h1 {
            font-size: 14px;
            color: #cccccc;
            font-weight: normal;
        }

        .sidebar {
            grid-area: sidebar;
            background: #252526;
            border-right: 1px solid #2d2d30;
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 10px;
            background: #2d2d30;
            border-bottom: 1px solid #3e3e42;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .sidebar-title {
            font-size: 11px;
            text-transform: uppercase;
            color: #cccccc;
            font-weight: 600;
        }

        .file-explorer {
            flex: 1;
            overflow-y: auto;
        }

        .file-tree {
            list-style: none;
            padding: 0;
        }

        .file-item {
            display: flex;
            align-items: center;
            padding: 4px 8px;
            cursor: pointer;
            font-size: 13px;
            color: #cccccc;
            border-left: 3px solid transparent;
        }

        .file-item:hover {
            background: #2a2d2e;
        }

        .file-item.active {
            background: #094771;
            border-left-color: #007acc;
        }

        .file-item i {
            margin-right: 6px;
            width: 16px;
            text-align: center;
        }

        .folder-item {
            font-weight: 500;
        }

        .folder-item.collapsed .fa-folder-open:before {
            content: "\f07b";
        }

        .editor-area {
            grid-area: editor-area;
            background: #1e1e1e;
            display: flex;
            flex-direction: column;
        }

        .tab-bar {
            background: #2d2d30;
            border-bottom: 1px solid #3e3e42;
            display: flex;
            min-height: 35px;
        }

        .tab {
            display: flex;
            align-items: center;
            padding: 0 12px;
            background: #2d2d30;
            border-right: 1px solid #3e3e42;
            cursor: pointer;
            font-size: 13px;
            color: #969696;
            min-width: 120px;
            position: relative;
        }

        .tab.active {
            background: #1e1e1e;
            color: #ffffff;
        }

        .tab-close {
            margin-left: 8px;
            opacity: 0;
            transition: opacity 0.2s;
        }

        .tab:hover .tab-close {
            opacity: 1;
        }

        .tab-close:hover {
            background: #e81123;
            color: white;
            border-radius: 3px;
        }

        .editor-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        .editor-toolbar {
            background: #2d2d30;
            padding: 8px 12px;
            border-bottom: 1px solid #3e3e42;
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .toolbar-btn {
            background: #0e639c;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 5px;
            transition: background 0.2s;
        }

        .toolbar-btn:hover {
            background: #1177bb;
        }

        .toolbar-btn.success {
            background: #16825d;
        }

        .toolbar-btn.success:hover {
            background: #1e9973;
        }

        .toolbar-btn.danger {
            background: #a1260d;
        }

        .toolbar-btn.danger:hover {
            background: #c42b1c;
        }

        .code-editor {
            flex: 1;
            background: #1e1e1e;
            position: relative;
        }

        .code-textarea {
            width: 100%;
            height: 100%;
            background: transparent;
            border: none;
            outline: none;
            color: #d4d4d4;
            font-family: 'Fira Code', 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
            padding: 20px;
            resize: none;
            tab-size: 4;
        }

        .terminal-area {
            grid-area: terminal;
            background: #0c0c0c;
            border-left: 1px solid #2d2d30;
            display: flex;
            flex-direction: column;
        }

        .terminal-header {
            background: #2d2d30;
            padding: 8px 12px;
            border-bottom: 1px solid #3e3e42;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .terminal-title {
            font-size: 12px;
            color: #cccccc;
        }

        .terminal-content {
            flex: 1;
            padding: 10px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 12px;
            overflow-y: auto;
            background: #0c0c0c;
            color: #cccccc;
        }

        .terminal-output {
            white-space: pre-wrap;
            margin-bottom: 10px;
        }

        .terminal-output.error {
            color: #f85149;
        }

        .terminal-output.success {
            color: #56d364;
        }

        .terminal-input {
            display: flex;
            align-items: center;
            margin-top: 10px;
        }

        .terminal-prompt {
            color: #56d364;
            margin-right: 5px;
        }

        .terminal-command {
            background: transparent;
            border: none;
            outline: none;
            color: #cccccc;
            font-family: inherit;
            font-size: inherit;
            flex: 1;
        }

        .ai-panel {
            background: #252526;
            border-top: 1px solid #3e3e42;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
        }

        .ai-input {
            width: 100%;
            background: #3c3c3c;
            border: 1px solid #5a5a5a;
            color: #d4d4d4;
            padding: 8px 12px;
            border-radius: 3px;
            font-size: 13px;
            margin-bottom: 10px;
        }

        .ai-response {
            background: #2d2d30;
            border-radius: 5px;
            padding: 12px;
            margin-top: 10px;
            font-size: 12px;
            line-height: 1.4;
        }

        .status-bar {
            background: #007acc;
            color: white;
            padding: 4px 12px;
            font-size: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .new-file-dialog {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #2d2d30;
            border: 1px solid #5a5a5a;
            border-radius: 5px;
            padding: 20px;
            z-index: 1000;
            min-width: 300px;
            display: none;
        }

        .dialog-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
            display: none;
        }

        .dialog-input {
            width: 100%;
            background: #3c3c3c;
            border: 1px solid #5a5a5a;
            color: #d4d4d4;
            padding: 8px;
            border-radius: 3px;
            margin: 10px 0;
        }

        .dialog-buttons {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 15px;
        }

        .dialog-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }

        .dialog-btn.primary {
            background: #0e639c;
            color: white;
        }

        .dialog-btn.secondary {
            background: #5a5a5a;
            color: white;
        }

        .scrollbar::-webkit-scrollbar {
            width: 8px;
        }

        .scrollbar::-webkit-scrollbar-track {
            background: #1e1e1e;
        }

        .scrollbar::-webkit-scrollbar-thumb {
            background: #424242;
            border-radius: 4px;
        }

        .scrollbar::-webkit-scrollbar-thumb:hover {
            background: #4f4f4f;
        }
    </style>
</head>
<body>
    <div class="ide-container">
        <!-- Title Bar -->
        <div class="titlebar">
            <h1>🤖 AI Code Assistant - Full IDE</h1>
        </div>

        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <span class="sidebar-title">Explorer</span>
                <i class="fas fa-plus" onclick="showNewFileDialog()" title="New File" style="cursor: pointer; color: #cccccc;"></i>
            </div>
            <div class="file-explorer scrollbar">
                <ul class="file-tree" id="fileTree">
                    <li class="file-item folder-item" onclick="toggleFolder(this)">
                        <i class="fas fa-folder-open"></i>
                        <span>my-project</span>
                    </li>
                    <li class="file-item" onclick="openFile('main.py', 'python')" style="padding-left: 24px;">
                        <i class="fab fa-python" style="color: #3776ab;"></i>
                        <span>main.py</span>
                    </li>
                    <li class="file-item" onclick="openFile('app.js', 'javascript')" style="padding-left: 24px;">
                        <i class="fab fa-js-square" style="color: #f7df1e;"></i>
                        <span>app.js</span>
                    </li>
                    <li class="file-item" onclick="openFile('style.css', 'css')" style="padding-left: 24px;">
                        <i class="fab fa-css3-alt" style="color: #1572b6;"></i>
                        <span>style.css</span>
                    </li>
                    <li class="file-item" onclick="openFile('README.md', 'markdown')" style="padding-left: 24px;">
                        <i class="fab fa-markdown" style="color: #083fa1;"></i>
                        <span>README.md</span>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Editor Area -->
        <div class="editor-area">
            <div class="tab-bar" id="tabBar">
                <div class="tab active" data-file="main.py">
                    <i class="fab fa-python" style="color: #3776ab; margin-right: 5px;"></i>
                    <span>main.py</span>
                    <i class="fas fa-times tab-close" onclick="closeTab('main.py')"></i>
                </div>
            </div>
            
            <div class="editor-content">
                <div class="editor-toolbar">
                    <button class="toolbar-btn" onclick="runCode()">
                        <i class="fas fa-play"></i>
                        Run Code
                    </button>
                    <button class="toolbar-btn success" onclick="saveFile()">
                        <i class="fas fa-save"></i>
                        Save
                    </button>
                    <button class="toolbar-btn" onclick="analyzeWithAI()">
                        <i class="fas fa-robot"></i>
                        AI Analyze
                    </button>
                    <button class="toolbar-btn" onclick="formatCode()">
                        <i class="fas fa-magic"></i>
                        Format
                    </button>
                    <select id="languageSelect" onchange="changeLanguage()" style="background: #3c3c3c; color: #d4d4d4; border: 1px solid #5a5a5a; padding: 4px 8px; border-radius: 3px;">
                        <option value="python">Python</option>
                        <option value="javascript">JavaScript</option>
                        <option value="java">Java</option>
                        <option value="cpp">C++</option>
                        <option value="csharp">C#</option>
                        <option value="go">Go</option>
                        <option value="rust">Rust</option>
                        <option value="html">HTML</option>
                        <option value="css">CSS</option>
                        <option value="sql">SQL</option>
                    </select>
                </div>
                
                <div class="code-editor">
                    <textarea class="code-textarea scrollbar" id="codeEditor" placeholder="Start coding here...">def hello_world():
    """
    A simple hello world function
    """
    print("Hello, World!")
    return "Hello from AI Code Assistant!"

def calculate_fibonacci(n):
    """
    Calculate the nth Fibonacci number
    """
    if n <= 1:
        return n
    return calculate_fibonacci(n-1) + calculate_fibonacci(n-2)

if __name__ == "__main__":
    hello_world()
    
    # Calculate first 10 Fibonacci numbers
    print("Fibonacci sequence:")
    for i in range(10):
        print(f"F({i}) = {calculate_fibonacci(i)}")
</textarea>
                </div>
            </div>

            <!-- AI Panel -->
            <div class="ai-panel">
                <input type="text" class="ai-input" id="aiInput" placeholder="Ask AI about your code... (Press Enter to send)" onkeypress="handleAIInput(event)">
                <div id="aiResponse" class="ai-response" style="display: none;"></div>
            </div>
        </div>

        <!-- Terminal Area -->
        <div class="terminal-area">
            <div class="terminal-header">
                <span class="terminal-title">Terminal</span>
                <button class="toolbar-btn" onclick="clearTerminal()" style="font-size: 10px; padding: 4px 8px;">
                    <i class="fas fa-trash"></i>
                    Clear
                </button>
            </div>
            <div class="terminal-content scrollbar" id="terminalContent">
                <div class="terminal-output">AI Code Assistant Terminal v1.0</div>
                <div class="terminal-output">Ready to execute your code!</div>
                <div class="terminal-output">Supported languages: Python, JavaScript, Java, C++, and more...</div>
                <div class="terminal-input">
                    <span class="terminal-prompt">$</span>
                    <input type="text" class="terminal-command" id="terminalInput" placeholder="Enter command..." onkeypress="handleTerminalInput(event)">
                </div>
            </div>
        </div>
    </div>

    <!-- New File Dialog -->
    <div class="dialog-overlay" id="dialogOverlay" onclick="hideNewFileDialog()"></div>
    <div class="new-file-dialog" id="newFileDialog">
        <h3 style="margin-bottom: 15px; color: #cccccc;">Create New File</h3>
        <input type="text" class="dialog-input" id="newFileName" placeholder="Enter file name (e.g., script.py)">
        <div class="dialog-buttons">
            <button class="dialog-btn secondary" onclick="hideNewFileDialog()">Cancel</button>
            <button class="dialog-btn primary" onclick="createNewFile()">Create</button>
        </div>
    </div>

    <!-- Include external libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/line-numbers/prism-line-numbers.min.js"></script>

    <script>
        // IDE State Management
        const IDE = {
            files: new Map(),
            activeFile: 'main.py',
            apiBase: 'http://localhost:8000',

            init() {
                // Initialize default files
                this.files.set('main.py', {
                    content: document.getElementById('codeEditor').value,
                    language: 'python',
                    saved: true
                });

                this.files.set('app.js', {
                    content: `// JavaScript Application
console.log("Hello from JavaScript!");

class Calculator {
    constructor() {
        this.history = [];
    }

    add(a, b) {
        const result = a + b;
        this.history.push(\`\${a} + \${b} = \${result}\`);
        return result;
    }

    getHistory() {
        return this.history;
    }
}

const calc = new Calculator();
console.log(calc.add(5, 3));
console.log(calc.getHistory());`,
                    language: 'javascript',
                    saved: true
                });

                this.files.set('style.css', {
                    content: `/* CSS Styles */
body {
    font-family: 'Arial', sans-serif;
    margin: 0;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    padding: 30px;
}

.header {
    text-align: center;
    margin-bottom: 30px;
}

.btn {
    background: #007acc;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    transition: background 0.3s;
}

.btn:hover {
    background: #005a9e;
}`,
                    language: 'css',
                    saved: true
                });

                this.files.set('README.md', {
                    content: `# AI Code Assistant Project

Welcome to your AI-powered development environment!

## Features

- 🤖 **AI Code Analysis** - Get intelligent insights about your code
- 🚀 **Code Execution** - Run your code directly in the browser
- 📁 **File Management** - Create, edit, and organize your files
- 🎨 **Syntax Highlighting** - Beautiful code display for multiple languages
- 💬 **AI Chat** - Ask questions about programming concepts

## Getting Started

1. **Write Code**: Use the editor to write your code
2. **Run Code**: Click the "Run Code" button to execute
3. **Ask AI**: Use the AI panel to get help with your code
4. **Save Files**: Save your work with Ctrl+S or the Save button

## Supported Languages

- Python
- JavaScript
- Java
- C++
- C#
- Go
- Rust
- HTML/CSS
- SQL
- And more!

## Tips

- Use \`Ctrl+S\` to save files quickly
- Press \`F5\` to run your code
- Ask the AI for help with debugging
- Create new files using the + button in the explorer

Happy coding! 🎉`,
                    language: 'markdown',
                    saved: true
                });

                this.updateStatusBar();
            },

            saveFile() {
                const content = document.getElementById('codeEditor').value;
                const file = this.files.get(this.activeFile);
                if (file) {
                    file.content = content;
                    file.saved = true;
                    this.updateTabTitle();
                    this.addTerminalOutput(`File '${this.activeFile}' saved successfully.`, 'success');
                }
            },

            updateTabTitle() {
                const tab = document.querySelector(`[data-file="${this.activeFile}"]`);
                if (tab) {
                    const file = this.files.get(this.activeFile);
                    const title = tab.querySelector('span');
                    title.textContent = file.saved ? this.activeFile : this.activeFile + ' •';
                }
            },

            updateStatusBar() {
                const file = this.files.get(this.activeFile);
                const content = document.getElementById('codeEditor').value;
                const lines = content.split('\\n').length;
                const chars = content.length;

                if (!document.querySelector('.status-bar')) {
                    const statusBar = document.createElement('div');
                    statusBar.className = 'status-bar';
                    statusBar.innerHTML = \`
                        <div>
                            <span id="fileInfo">\${this.activeFile} • \${file?.language || 'text'}</span>
                        </div>
                        <div>
                            <span id="statsInfo">Lines: \${lines} • Characters: \${chars}</span>
                        </div>
                    \`;
                    document.body.appendChild(statusBar);
                } else {
                    document.getElementById('fileInfo').textContent = \`\${this.activeFile} • \${file?.language || 'text'}\`;
                    document.getElementById('statsInfo').textContent = \`Lines: \${lines} • Characters: \${chars}\`;
                }
            },

            addTerminalOutput(text, type = 'normal') {
                const terminal = document.getElementById('terminalContent');
                const output = document.createElement('div');
                output.className = \`terminal-output \${type}\`;
                output.textContent = text;
                terminal.insertBefore(output, terminal.lastElementChild);
                terminal.scrollTop = terminal.scrollHeight;
            }
        };

        // File Management Functions
        function openFile(fileName, language) {
            // Save current file content
            if (IDE.activeFile) {
                const currentContent = document.getElementById('codeEditor').value;
                const currentFile = IDE.files.get(IDE.activeFile);
                if (currentFile) {
                    currentFile.content = currentContent;
                    currentFile.saved = currentFile.content === currentContent;
                }
            }

            // Switch to new file
            IDE.activeFile = fileName;
            const file = IDE.files.get(fileName);

            if (file) {
                document.getElementById('codeEditor').value = file.content;
                document.getElementById('languageSelect').value = file.language;
            }

            // Update UI
            updateActiveTab(fileName);
            addTabIfNotExists(fileName, language);
            updateFileExplorerSelection(fileName);
            IDE.updateStatusBar();
        }

        function addTabIfNotExists(fileName, language) {
            const existingTab = document.querySelector(\`[data-file="\${fileName}"]\`);
            if (!existingTab) {
                const tabBar = document.getElementById('tabBar');
                const tab = document.createElement('div');
                tab.className = 'tab';
                tab.setAttribute('data-file', fileName);
                tab.innerHTML = \`
                    <i class="\${getFileIcon(language)}" style="color: \${getFileColor(language)}; margin-right: 5px;"></i>
                    <span>\${fileName}</span>
                    <i class="fas fa-times tab-close" onclick="closeTab('\${fileName}')"></i>
                \`;
                tab.onclick = () => openFile(fileName, language);
                tabBar.appendChild(tab);
            }
        }

        function updateActiveTab(fileName) {
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            const activeTab = document.querySelector(\`[data-file="\${fileName}"]\`);
            if (activeTab) {
                activeTab.classList.add('active');
            }
        }

        function updateFileExplorerSelection(fileName) {
            document.querySelectorAll('.file-item').forEach(item => {
                item.classList.remove('active');
            });
            // Find and activate the file item (simplified)
            const fileItems = document.querySelectorAll('.file-item');
            fileItems.forEach(item => {
                if (item.textContent.trim() === fileName) {
                    item.classList.add('active');
                }
            });
        }

        function closeTab(fileName) {
            const tab = document.querySelector(\`[data-file="\${fileName}"]\`);
            if (tab) {
                tab.remove();

                // If this was the active tab, switch to another
                if (IDE.activeFile === fileName) {
                    const remainingTabs = document.querySelectorAll('.tab');
                    if (remainingTabs.length > 0) {
                        const firstTab = remainingTabs[0];
                        const newFileName = firstTab.getAttribute('data-file');
                        openFile(newFileName, IDE.files.get(newFileName)?.language || 'text');
                    } else {
                        // No tabs left, create a new file
                        createNewFile('untitled.txt', 'text');
                    }
                }
            }
        }

        function getFileIcon(language) {
            const icons = {
                'python': 'fab fa-python',
                'javascript': 'fab fa-js-square',
                'java': 'fab fa-java',
                'html': 'fab fa-html5',
                'css': 'fab fa-css3-alt',
                'markdown': 'fab fa-markdown',
                'json': 'fas fa-code',
                'xml': 'fas fa-code',
                'sql': 'fas fa-database',
                'cpp': 'fas fa-code',
                'c': 'fas fa-code',
                'csharp': 'fas fa-code',
                'go': 'fas fa-code',
                'rust': 'fas fa-code'
            };
            return icons[language] || 'fas fa-file-code';
        }

        function getFileColor(language) {
            const colors = {
                'python': '#3776ab',
                'javascript': '#f7df1e',
                'java': '#ed8b00',
                'html': '#e34f26',
                'css': '#1572b6',
                'markdown': '#083fa1',
                'json': '#000000',
                'sql': '#336791'
            };
            return colors[language] || '#cccccc';
        }

        // New File Dialog Functions
        function showNewFileDialog() {
            document.getElementById('dialogOverlay').style.display = 'block';
            document.getElementById('newFileDialog').style.display = 'block';
            document.getElementById('newFileName').focus();
        }

        function hideNewFileDialog() {
            document.getElementById('dialogOverlay').style.display = 'none';
            document.getElementById('newFileDialog').style.display = 'none';
            document.getElementById('newFileName').value = '';
        }

        function createNewFile() {
            const fileName = document.getElementById('newFileName').value.trim();
            if (!fileName) {
                alert('Please enter a file name');
                return;
            }

            const extension = fileName.split('.').pop().toLowerCase();
            const language = getLanguageFromExtension(extension);

            IDE.files.set(fileName, {
                content: getTemplateForLanguage(language),
                language: language,
                saved: false
            });

            // Add to file explorer
            addFileToExplorer(fileName, language);

            // Open the new file
            openFile(fileName, language);

            hideNewFileDialog();
            IDE.addTerminalOutput(\`Created new file: \${fileName}\`, 'success');
        }

        function addFileToExplorer(fileName, language) {
            const fileTree = document.getElementById('fileTree');
            const fileItem = document.createElement('li');
            fileItem.className = 'file-item';
            fileItem.style.paddingLeft = '24px';
            fileItem.onclick = () => openFile(fileName, language);
            fileItem.innerHTML = \`
                <i class="\${getFileIcon(language)}" style="color: \${getFileColor(language)};"></i>
                <span>\${fileName}</span>
            \`;
            fileTree.appendChild(fileItem);
        }

        function getLanguageFromExtension(ext) {
            const extensions = {
                'py': 'python',
                'js': 'javascript',
                'ts': 'typescript',
                'java': 'java',
                'cpp': 'cpp',
                'c': 'c',
                'cs': 'csharp',
                'go': 'go',
                'rs': 'rust',
                'html': 'html',
                'css': 'css',
                'md': 'markdown',
                'json': 'json',
                'xml': 'xml',
                'sql': 'sql'
            };
            return extensions[ext] || 'text';
        }

        function getTemplateForLanguage(language) {
            const templates = {
                'python': \`# Python Script
def main():
    print("Hello, World!")

if __name__ == "__main__":
    main()
\`,
                'javascript': \`// JavaScript Application
console.log("Hello, World!");

function main() {
    // Your code here
}

main();
\`,
                'java': \`public class Main {
    public static void main(String[] args) {
        System.out.println("Hello, World!");
    }
}
\`,
                'html': \`<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
    <h1>Hello, World!</h1>
</body>
</html>
\`,
                'css': \`/* CSS Styles */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 20px;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
}
\`,
                'markdown': \`# Title

## Subtitle

This is a markdown document.

- List item 1
- List item 2
- List item 3

\`\`\`python
print("Hello, World!")
\`\`\`
\`
            };
            return templates[language] || '// New file\\n';
        }

        // Code Execution Functions
        async function runCode() {
            const code = document.getElementById('codeEditor').value;
            const language = document.getElementById('languageSelect').value;

            if (!code.trim()) {
                IDE.addTerminalOutput('No code to execute.', 'error');
                return;
            }

            IDE.addTerminalOutput(\`Running \${language} code...\`, 'normal');

            try {
                if (language === 'javascript') {
                    // Try server execution first, fallback to local
                    try {
                        await executeCodeOnServer(code, language);
                    } catch (error) {
                        IDE.addTerminalOutput('Server execution failed, running locally...', 'normal');
                        runJavaScript(code);
                    }
                } else {
                    // Use server execution for all other languages
                    await executeCodeOnServer(code, language);
                }
            } catch (error) {
                IDE.addTerminalOutput(\`Execution error: \${error.message}\`, 'error');
            }
        }

        function runJavaScript(code) {
            try {
                // Capture console.log output
                const originalLog = console.log;
                const outputs = [];

                console.log = (...args) => {
                    outputs.push(args.map(arg =>
                        typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
                    ).join(' '));
                };

                // Execute the code
                const result = eval(code);

                // Restore console.log
                console.log = originalLog;

                // Display outputs
                if (outputs.length > 0) {
                    outputs.forEach(output => {
                        IDE.addTerminalOutput(output, 'success');
                    });
                } else if (result !== undefined) {
                    IDE.addTerminalOutput(String(result), 'success');
                }

                IDE.addTerminalOutput('JavaScript execution completed.', 'success');

            } catch (error) {
                IDE.addTerminalOutput(\`JavaScript Error: \${error.message}\`, 'error');
            }
        }

        async function runPython(code) {
            return await executeCodeOnServer(code, 'python');
        }

        async function executeCodeOnServer(code, language) {
            try {
                const response = await fetch(\`\${IDE.apiBase}/ai/execute\`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        code: code,
                        language: language,
                        input_data: ""
                    })
                });

                if (response.ok) {
                    const result = await response.json();

                    if (result.success) {
                        if (result.output) {
                            IDE.addTerminalOutput(result.output, 'success');
                        }
                        IDE.addTerminalOutput(\`Execution completed in \${result.execution_time}s\`, 'success');
                    } else {
                        IDE.addTerminalOutput(\`Execution failed: \${result.error}\`, 'error');
                    }
                } else {
                    throw new Error(\`Server error: \${response.status}\`);
                }
            } catch (error) {
                IDE.addTerminalOutput(\`Execution error: \${error.message}\`, 'error');
                // Fallback to local simulation
                await simulateExecution(code, language);
            }
        }

        async function simulateExecution(code, language) {
            IDE.addTerminalOutput(\`Simulating \${language} execution...\`, 'normal');

            // Simulate some processing time
            await new Promise(resolve => setTimeout(resolve, 1000));

            IDE.addTerminalOutput(\`\${language} code would execute here.\`, 'normal');
            IDE.addTerminalOutput('For full execution, install the appropriate runtime.', 'normal');
        }

        // AI Integration Functions
        async function analyzeWithAI() {
            const code = document.getElementById('codeEditor').value;
            const language = document.getElementById('languageSelect').value;

            if (!code.trim()) {
                alert('No code to analyze');
                return;
            }

            try {
                const response = await fetch(\`\${IDE.apiBase}/ai/analyze\`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        code: code,
                        language: language,
                        analysis_type: 'explain'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    showAIResponse(\`Code Analysis:\\n\\n\${data.result}\`);
                } else {
                    throw new Error(\`Server error: \${response.status}\`);
                }
            } catch (error) {
                showAIResponse(\`Error analyzing code: \${error.message}\`);
            }
        }

        async function handleAIInput(event) {
            if (event.key === 'Enter') {
                const input = document.getElementById('aiInput');
                const prompt = input.value.trim();

                if (!prompt) return;

                input.value = '';

                // Add user message
                showAIResponse(\`You: \${prompt}\`, false);

                try {
                    const response = await fetch(\`\${IDE.apiBase}/ai/chat\`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            prompt: prompt,
                            model: 'llama3-8b-8192'
                        })
                    });

                    if (response.ok) {
                        const data = await response.json();
                        showAIResponse(\`AI: \${data.response}\`);
                    } else {
                        throw new Error(\`Server error: \${response.status}\`);
                    }
                } catch (error) {
                    showAIResponse(\`Error: \${error.message}\`);
                }
            }
        }

        function showAIResponse(text, isAI = true) {
            const responseDiv = document.getElementById('aiResponse');
            responseDiv.style.display = 'block';

            const messageDiv = document.createElement('div');
            messageDiv.style.marginBottom = '10px';
            messageDiv.style.padding = '8px';
            messageDiv.style.borderRadius = '5px';
            messageDiv.style.backgroundColor = isAI ? '#2d2d30' : '#094771';
            messageDiv.style.whiteSpace = 'pre-wrap';
            messageDiv.textContent = text;

            responseDiv.appendChild(messageDiv);
            responseDiv.scrollTop = responseDiv.scrollHeight;
        }

        // Utility Functions
        function saveFile() {
            IDE.saveFile();
        }

        function formatCode() {
            const code = document.getElementById('codeEditor').value;
            const language = document.getElementById('languageSelect').value;

            // Simple formatting for demonstration
            let formatted = code;

            if (language === 'javascript' || language === 'java' || language === 'cpp') {
                // Add basic formatting
                formatted = formatted
                    .replace(/;/g, ';\\n')
                    .replace(/{/g, '{\\n')
                    .replace(/}/g, '\\n}\\n')
                    .replace(/\\n\\s*\\n/g, '\\n');
            }

            document.getElementById('codeEditor').value = formatted;
            IDE.addTerminalOutput('Code formatted.', 'success');
        }

        function changeLanguage() {
            const language = document.getElementById('languageSelect').value;
            const file = IDE.files.get(IDE.activeFile);
            if (file) {
                file.language = language;
                IDE.updateStatusBar();
            }
        }

        function toggleFolder(element) {
            element.classList.toggle('collapsed');
        }

        function clearTerminal() {
            const terminal = document.getElementById('terminalContent');
            const outputs = terminal.querySelectorAll('.terminal-output');
            outputs.forEach(output => output.remove());

            IDE.addTerminalOutput('Terminal cleared.', 'normal');
        }

        function handleTerminalInput(event) {
            if (event.key === 'Enter') {
                const input = event.target;
                const command = input.value.trim();

                if (!command) return;

                IDE.addTerminalOutput(\`$ \${command}\`, 'normal');

                // Handle basic commands
                if (command === 'clear') {
                    clearTerminal();
                } else if (command === 'ls' || command === 'dir') {
                    const files = Array.from(IDE.files.keys());
                    IDE.addTerminalOutput(files.join('\\n'), 'success');
                } else if (command.startsWith('cat ')) {
                    const fileName = command.substring(4);
                    const file = IDE.files.get(fileName);
                    if (file) {
                        IDE.addTerminalOutput(file.content, 'normal');
                    } else {
                        IDE.addTerminalOutput(\`File not found: \${fileName}\`, 'error');
                    }
                } else if (command === 'help') {
                    IDE.addTerminalOutput(\`Available commands:
- clear: Clear terminal
- ls/dir: List files
- cat <filename>: Show file content
- help: Show this help\`, 'normal');
                } else {
                    IDE.addTerminalOutput(\`Command not found: \${command}\`, 'error');
                }

                input.value = '';
            }
        }

        // Keyboard Shortcuts
        document.addEventListener('keydown', function(event) {
            // Ctrl+S or Cmd+S to save
            if ((event.ctrlKey || event.metaKey) && event.key === 's') {
                event.preventDefault();
                saveFile();
            }

            // F5 to run code
            if (event.key === 'F5') {
                event.preventDefault();
                runCode();
            }

            // Ctrl+N or Cmd+N for new file
            if ((event.ctrlKey || event.metaKey) && event.key === 'n') {
                event.preventDefault();
                showNewFileDialog();
            }
        });

        // Initialize IDE when page loads
        document.addEventListener('DOMContentLoaded', function() {
            IDE.init();

            // Update status bar when typing
            document.getElementById('codeEditor').addEventListener('input', function() {
                IDE.updateStatusBar();

                // Mark file as unsaved
                const file = IDE.files.get(IDE.activeFile);
                if (file) {
                    file.saved = false;
                    IDE.updateTabTitle();
                }
            });
        });
    </script>
</body>
</html>
