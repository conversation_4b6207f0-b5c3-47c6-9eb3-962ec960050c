# PowerShell script to install AI Code Assistant VS Code Extension
# Run this script to install the extension without Node.js

Write-Host "🤖 AI Code Assistant - VS Code Extension Installer" -ForegroundColor Cyan
Write-Host "=" * 60 -ForegroundColor Cyan

# Get VS Code extensions directory
$vsCodeExtensionsDir = "$env:USERPROFILE\.vscode\extensions"
$extensionDir = "$vsCodeExtensionsDir\ai-code-assistant-1.0.0"

Write-Host "📁 VS Code Extensions Directory: $vsCodeExtensionsDir" -ForegroundColor Yellow

# Check if VS Code is installed
if (!(Test-Path $vsCodeExtensionsDir)) {
    Write-Host "❌ VS Code extensions directory not found!" -ForegroundColor Red
    Write-Host "Please make sure VS Code is installed and has been run at least once." -ForegroundColor Red
    exit 1
}

# Create extension directory
Write-Host "📦 Creating extension directory..." -ForegroundColor Green
if (Test-Path $extensionDir) {
    Write-Host "⚠️  Extension directory already exists. Removing old version..." -ForegroundColor Yellow
    Remove-Item $extensionDir -Recurse -Force
}

New-Item -ItemType Directory -Path $extensionDir -Force | Out-Null

# Copy extension files
Write-Host "📋 Copying extension files..." -ForegroundColor Green

# Copy main files
Copy-Item "package.json" $extensionDir
Copy-Item "src" $extensionDir -Recurse

# Create a simple node_modules structure for axios
$nodeModulesDir = "$extensionDir\node_modules\axios"
New-Item -ItemType Directory -Path $nodeModulesDir -Force | Out-Null

# Create a simple axios mock (since we can't install npm packages)
$axiosMock = @'
// Simple axios mock for VS Code extension
const https = require('https');
const http = require('http');
const url = require('url');

function axios(config) {
    return new Promise((resolve, reject) => {
        const parsedUrl = url.parse(config.url || config);
        const options = {
            hostname: parsedUrl.hostname,
            port: parsedUrl.port,
            path: parsedUrl.path,
            method: config.method || 'GET',
            headers: config.headers || {}
        };

        if (config.data) {
            const data = JSON.stringify(config.data);
            options.headers['Content-Type'] = 'application/json';
            options.headers['Content-Length'] = Buffer.byteLength(data);
        }

        const protocol = parsedUrl.protocol === 'https:' ? https : http;
        const req = protocol.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => body += chunk);
            res.on('end', () => {
                try {
                    const data = JSON.parse(body);
                    resolve({ data, status: res.statusCode });
                } catch (e) {
                    resolve({ data: body, status: res.statusCode });
                }
            });
        });

        req.on('error', reject);

        if (config.data) {
            req.write(JSON.stringify(config.data));
        }

        req.end();
    });
}

axios.post = (url, data, config = {}) => {
    return axios({ ...config, url, method: 'POST', data });
};

axios.get = (url, config = {}) => {
    return axios({ ...config, url, method: 'GET' });
};

module.exports = axios;
'@

$axiosMock | Out-File -FilePath "$nodeModulesDir\index.js" -Encoding UTF8

# Create package.json for axios
$axiosPackage = @'
{
  "name": "axios",
  "version": "1.6.0",
  "main": "index.js"
}
'@

$axiosPackage | Out-File -FilePath "$nodeModulesDir\package.json" -Encoding UTF8

Write-Host "✅ Extension files copied successfully!" -ForegroundColor Green

# Instructions
Write-Host ""
Write-Host "🎉 Installation Complete!" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Restart VS Code completely (close all windows)" -ForegroundColor White
Write-Host "2. Open VS Code again" -ForegroundColor White
Write-Host "3. The AI Code Assistant should be active!" -ForegroundColor White
Write-Host ""
Write-Host "How to use:" -ForegroundColor Yellow
Write-Host "• Look for the 🤖 robot icon in the status bar" -ForegroundColor White
Write-Host "• Press Ctrl+Shift+P and type 'AI Assistant'" -ForegroundColor White
Write-Host "• Right-click on selected code for AI options" -ForegroundColor White
Write-Host "• Use Ctrl+Shift+A for quick AI chat" -ForegroundColor White
Write-Host ""
Write-Host "Make sure the Python backend is running:" -ForegroundColor Yellow
Write-Host "cd python-backend; python main.py" -ForegroundColor White
Write-Host ""
Write-Host "Extension installed at: $extensionDir" -ForegroundColor Cyan
