"""
Language Server Protocol implementation for AI Code Assistant
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class Position:
    """LSP Position"""
    line: int
    character: int


@dataclass
class Range:
    """LSP Range"""
    start: Position
    end: Position


@dataclass
class TextEdit:
    """LSP TextEdit"""
    range: Range
    new_text: str


@dataclass
class CompletionItem:
    """LSP CompletionItem"""
    label: str
    kind: Optional[int] = None
    detail: Optional[str] = None
    documentation: Optional[str] = None
    insert_text: Optional[str] = None
    text_edit: Optional[TextEdit] = None


@dataclass
class Diagnostic:
    """LSP Diagnostic"""
    range: Range
    message: str
    severity: Optional[int] = None
    code: Optional[Union[int, str]] = None
    source: Optional[str] = None


class LanguageServer:
    """Language Server Protocol implementation"""
    
    def __init__(self):
        self.documents: Dict[str, str] = {}
        self.diagnostics: Dict[str, List[Diagnostic]] = {}
        self.capabilities = {
            "textDocumentSync": 1,  # Full sync
            "completionProvider": {
                "resolveProvider": True,
                "triggerCharacters": [".", "(", "[", " "]
            },
            "hoverProvider": True,
            "definitionProvider": True,
            "referencesProvider": True,
            "documentSymbolProvider": True,
            "workspaceSymbolProvider": True,
            "codeActionProvider": True,
            "documentFormattingProvider": True,
            "documentRangeFormattingProvider": True,
            "renameProvider": True,
            "foldingRangeProvider": True,
            "selectionRangeProvider": True
        }
    
    async def initialize(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Handle initialize request"""
        logger.info("Language server initializing...")
        
        return {
            "capabilities": self.capabilities,
            "serverInfo": {
                "name": "AI Code Assistant Language Server",
                "version": "1.0.0"
            }
        }
    
    async def text_document_did_open(self, params: Dict[str, Any]):
        """Handle textDocument/didOpen notification"""
        text_document = params["textDocument"]
        uri = text_document["uri"]
        text = text_document["text"]
        
        self.documents[uri] = text
        logger.debug(f"Opened document: {uri}")
        
        # Trigger diagnostics
        await self._analyze_document(uri, text)
    
    async def text_document_did_change(self, params: Dict[str, Any]):
        """Handle textDocument/didChange notification"""
        text_document = params["textDocument"]
        uri = text_document["uri"]
        changes = params["contentChanges"]
        
        # For full sync, we just replace the entire content
        if changes and "text" in changes[0]:
            self.documents[uri] = changes[0]["text"]
            logger.debug(f"Updated document: {uri}")
            
            # Trigger diagnostics
            await self._analyze_document(uri, changes[0]["text"])
    
    async def text_document_did_close(self, params: Dict[str, Any]):
        """Handle textDocument/didClose notification"""
        text_document = params["textDocument"]
        uri = text_document["uri"]
        
        if uri in self.documents:
            del self.documents[uri]
        if uri in self.diagnostics:
            del self.diagnostics[uri]
        
        logger.debug(f"Closed document: {uri}")
    
    async def text_document_completion(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Handle textDocument/completion request"""
        text_document = params["textDocument"]
        position = params["position"]
        uri = text_document["uri"]
        
        if uri not in self.documents:
            return {"items": []}
        
        text = self.documents[uri]
        line = position["line"]
        character = position["character"]
        
        # Get completion context
        lines = text.split('\n')
        if line >= len(lines):
            return {"items": []}
        
        current_line = lines[line]
        prefix = current_line[:character]
        
        # Generate completions based on context
        completions = await self._generate_completions(text, line, character, uri)
        
        return {"items": completions}
    
    async def text_document_hover(self, params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Handle textDocument/hover request"""
        text_document = params["textDocument"]
        position = params["position"]
        uri = text_document["uri"]
        
        if uri not in self.documents:
            return None
        
        text = self.documents[uri]
        hover_info = await self._get_hover_info(text, position, uri)
        
        if hover_info:
            return {
                "contents": {
                    "kind": "markdown",
                    "value": hover_info
                }
            }
        
        return None
    
    async def text_document_definition(self, params: Dict[str, Any]) -> Optional[List[Dict[str, Any]]]:
        """Handle textDocument/definition request"""
        text_document = params["textDocument"]
        position = params["position"]
        uri = text_document["uri"]
        
        if uri not in self.documents:
            return None
        
        # This is a simplified implementation
        # In a real LSP, you'd analyze the code to find actual definitions
        return []
    
    async def text_document_references(self, params: Dict[str, Any]) -> Optional[List[Dict[str, Any]]]:
        """Handle textDocument/references request"""
        text_document = params["textDocument"]
        position = params["position"]
        uri = text_document["uri"]
        
        if uri not in self.documents:
            return None
        
        # This is a simplified implementation
        return []
    
    async def text_document_document_symbol(self, params: Dict[str, Any]) -> Optional[List[Dict[str, Any]]]:
        """Handle textDocument/documentSymbol request"""
        text_document = params["textDocument"]
        uri = text_document["uri"]
        
        if uri not in self.documents:
            return None
        
        text = self.documents[uri]
        symbols = await self._extract_symbols(text, uri)
        
        return symbols
    
    async def text_document_code_action(self, params: Dict[str, Any]) -> Optional[List[Dict[str, Any]]]:
        """Handle textDocument/codeAction request"""
        text_document = params["textDocument"]
        range_param = params["range"]
        context = params.get("context", {})
        uri = text_document["uri"]
        
        if uri not in self.documents:
            return None
        
        actions = []
        
        # Add AI-powered code actions
        actions.extend([
            {
                "title": "Explain with AI",
                "kind": "quickfix",
                "command": {
                    "title": "Explain with AI",
                    "command": "aiCodeAssistant.explainCode",
                    "arguments": [uri, range_param]
                }
            },
            {
                "title": "Refactor with AI",
                "kind": "refactor",
                "command": {
                    "title": "Refactor with AI",
                    "command": "aiCodeAssistant.refactorCode",
                    "arguments": [uri, range_param]
                }
            },
            {
                "title": "Generate Tests",
                "kind": "source",
                "command": {
                    "title": "Generate Tests",
                    "command": "aiCodeAssistant.generateTests",
                    "arguments": [uri, range_param]
                }
            }
        ])
        
        return actions
    
    async def _analyze_document(self, uri: str, text: str):
        """Analyze document and generate diagnostics"""
        try:
            # This is where you'd integrate with your code analyzer
            # For now, we'll do basic syntax checking
            
            diagnostics = []
            
            # Example: Check for common issues
            lines = text.split('\n')
            for i, line in enumerate(lines):
                # Check for TODO comments
                if 'TODO' in line.upper():
                    diagnostics.append(Diagnostic(
                        range=Range(
                            start=Position(line=i, character=0),
                            end=Position(line=i, character=len(line))
                        ),
                        message="TODO comment found",
                        severity=3,  # Information
                        source="ai-assistant"
                    ))
            
            self.diagnostics[uri] = diagnostics
            
        except Exception as e:
            logger.error(f"Error analyzing document {uri}: {e}")
    
    async def _generate_completions(self, text: str, line: int, character: int, uri: str) -> List[Dict[str, Any]]:
        """Generate AI-powered completions"""
        try:
            # Get context around cursor
            lines = text.split('\n')
            context_lines = lines[max(0, line-5):line+1]
            context = '\n'.join(context_lines)
            
            # This would integrate with your AI service
            # For now, return some basic completions
            completions = [
                {
                    "label": "print()",
                    "kind": 3,  # Function
                    "detail": "Print function",
                    "insertText": "print($1)",
                    "insertTextFormat": 2  # Snippet
                },
                {
                    "label": "if __name__ == '__main__':",
                    "kind": 15,  # Snippet
                    "detail": "Main guard",
                    "insertText": "if __name__ == '__main__':\n    $1"
                }
            ]
            
            return completions
            
        except Exception as e:
            logger.error(f"Error generating completions: {e}")
            return []
    
    async def _get_hover_info(self, text: str, position: Dict[str, Any], uri: str) -> Optional[str]:
        """Get hover information for symbol at position"""
        try:
            line = position["line"]
            character = position["character"]
            
            lines = text.split('\n')
            if line >= len(lines):
                return None
            
            current_line = lines[line]
            
            # Simple word extraction
            start = character
            end = character
            
            # Find word boundaries
            while start > 0 and current_line[start-1].isalnum():
                start -= 1
            while end < len(current_line) and current_line[end].isalnum():
                end += 1
            
            if start == end:
                return None
            
            word = current_line[start:end]
            
            # This would integrate with your AI service for intelligent hover info
            return f"**{word}**\n\nAI-powered information about this symbol would appear here."
            
        except Exception as e:
            logger.error(f"Error getting hover info: {e}")
            return None
    
    async def _extract_symbols(self, text: str, uri: str) -> List[Dict[str, Any]]:
        """Extract symbols from document"""
        try:
            symbols = []
            lines = text.split('\n')
            
            for i, line in enumerate(lines):
                line = line.strip()
                
                # Python function definitions
                if line.startswith('def '):
                    func_name = line.split('(')[0].replace('def ', '')
                    symbols.append({
                        "name": func_name,
                        "kind": 12,  # Function
                        "location": {
                            "uri": uri,
                            "range": {
                                "start": {"line": i, "character": 0},
                                "end": {"line": i, "character": len(line)}
                            }
                        }
                    })
                
                # Python class definitions
                elif line.startswith('class '):
                    class_name = line.split('(')[0].replace('class ', '').replace(':', '')
                    symbols.append({
                        "name": class_name,
                        "kind": 5,  # Class
                        "location": {
                            "uri": uri,
                            "range": {
                                "start": {"line": i, "character": 0},
                                "end": {"line": i, "character": len(line)}
                            }
                        }
                    })
            
            return symbols
            
        except Exception as e:
            logger.error(f"Error extracting symbols: {e}")
            return []
