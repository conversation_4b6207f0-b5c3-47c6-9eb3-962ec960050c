"""
Context Manager for maintaining conversation and code context
"""

import logging
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from collections import defaultdict
import json

logger = logging.getLogger(__name__)


@dataclass
class ConversationMessage:
    """Represents a single message in a conversation"""
    role: str  # 'user' or 'assistant'
    content: str
    timestamp: float
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class CodeContext:
    """Represents code context information"""
    file_path: str
    language: str
    content: str
    line_number: Optional[int] = None
    selection_start: Optional[int] = None
    selection_end: Optional[int] = None
    timestamp: float = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()


@dataclass
class WorkspaceContext:
    """Represents workspace-level context"""
    workspace_path: str
    project_type: Optional[str] = None
    dependencies: List[str] = None
    recent_files: List[str] = None
    git_branch: Optional[str] = None
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []
        if self.recent_files is None:
            self.recent_files = []


class ContextManager:
    """Manages conversation history and code context"""
    
    def __init__(self, max_conversation_length: int = 50, max_context_age: int = 3600):
        self.conversations: Dict[str, List[ConversationMessage]] = defaultdict(list)
        self.code_contexts: Dict[str, List[CodeContext]] = defaultdict(list)
        self.workspace_contexts: Dict[str, WorkspaceContext] = {}
        
        self.max_conversation_length = max_conversation_length
        self.max_context_age = max_context_age  # seconds
    
    def add_conversation_message(self, session_id: str, role: str, content: str, metadata: Optional[Dict] = None):
        """Add a message to the conversation history"""
        message = ConversationMessage(
            role=role,
            content=content,
            timestamp=time.time(),
            metadata=metadata or {}
        )
        
        self.conversations[session_id].append(message)
        
        # Trim conversation if it gets too long
        if len(self.conversations[session_id]) > self.max_conversation_length:
            self.conversations[session_id] = self.conversations[session_id][-self.max_conversation_length:]
        
        logger.debug(f"Added {role} message to session {session_id}")
    
    def get_conversation_history(self, session_id: str, limit: Optional[int] = None) -> List[ConversationMessage]:
        """Get conversation history for a session"""
        messages = self.conversations.get(session_id, [])
        
        if limit:
            messages = messages[-limit:]
        
        # Filter out old messages
        current_time = time.time()
        messages = [
            msg for msg in messages 
            if current_time - msg.timestamp < self.max_context_age
        ]
        
        return messages
    
    def add_code_context(self, session_id: str, file_path: str, language: str, content: str, 
                        line_number: Optional[int] = None, selection_start: Optional[int] = None, 
                        selection_end: Optional[int] = None):
        """Add code context for a session"""
        context = CodeContext(
            file_path=file_path,
            language=language,
            content=content,
            line_number=line_number,
            selection_start=selection_start,
            selection_end=selection_end
        )
        
        self.code_contexts[session_id].append(context)
        
        # Keep only recent contexts
        current_time = time.time()
        self.code_contexts[session_id] = [
            ctx for ctx in self.code_contexts[session_id]
            if current_time - ctx.timestamp < self.max_context_age
        ]
        
        logger.debug(f"Added code context for {file_path} to session {session_id}")
    
    def get_code_contexts(self, session_id: str, limit: Optional[int] = 5) -> List[CodeContext]:
        """Get recent code contexts for a session"""
        contexts = self.code_contexts.get(session_id, [])
        
        if limit:
            contexts = contexts[-limit:]
        
        return contexts
    
    def set_workspace_context(self, session_id: str, workspace_path: str, project_type: Optional[str] = None,
                             dependencies: Optional[List[str]] = None, recent_files: Optional[List[str]] = None,
                             git_branch: Optional[str] = None):
        """Set workspace context for a session"""
        self.workspace_contexts[session_id] = WorkspaceContext(
            workspace_path=workspace_path,
            project_type=project_type,
            dependencies=dependencies or [],
            recent_files=recent_files or [],
            git_branch=git_branch
        )
        
        logger.debug(f"Set workspace context for session {session_id}")
    
    def get_workspace_context(self, session_id: str) -> Optional[WorkspaceContext]:
        """Get workspace context for a session"""
        return self.workspace_contexts.get(session_id)
    
    def build_context_string(self, session_id: str, include_conversation: bool = True, 
                           include_code: bool = True, include_workspace: bool = True) -> str:
        """Build a comprehensive context string for AI processing"""
        context_parts = []
        
        # Add workspace context
        if include_workspace:
            workspace_ctx = self.get_workspace_context(session_id)
            if workspace_ctx:
                context_parts.append("=== Workspace Context ===")
                context_parts.append(f"Path: {workspace_ctx.workspace_path}")
                if workspace_ctx.project_type:
                    context_parts.append(f"Project Type: {workspace_ctx.project_type}")
                if workspace_ctx.git_branch:
                    context_parts.append(f"Git Branch: {workspace_ctx.git_branch}")
                if workspace_ctx.dependencies:
                    context_parts.append(f"Dependencies: {', '.join(workspace_ctx.dependencies[:10])}")
                if workspace_ctx.recent_files:
                    context_parts.append(f"Recent Files: {', '.join(workspace_ctx.recent_files[:5])}")
                context_parts.append("")
        
        # Add code contexts
        if include_code:
            code_contexts = self.get_code_contexts(session_id)
            if code_contexts:
                context_parts.append("=== Code Context ===")
                for ctx in code_contexts:
                    context_parts.append(f"File: {ctx.file_path} ({ctx.language})")
                    if ctx.line_number:
                        context_parts.append(f"Line: {ctx.line_number}")
                    if ctx.selection_start is not None and ctx.selection_end is not None:
                        selected_content = ctx.content[ctx.selection_start:ctx.selection_end]
                        context_parts.append(f"Selected Code:\n```{ctx.language}\n{selected_content}\n```")
                    else:
                        # Include a snippet of the file content
                        lines = ctx.content.split('\n')
                        if len(lines) > 20:
                            snippet = '\n'.join(lines[:10] + ['...'] + lines[-10:])
                        else:
                            snippet = ctx.content
                        context_parts.append(f"Code:\n```{ctx.language}\n{snippet}\n```")
                    context_parts.append("")
        
        # Add conversation history
        if include_conversation:
            conversation = self.get_conversation_history(session_id, limit=10)
            if conversation:
                context_parts.append("=== Conversation History ===")
                for msg in conversation:
                    context_parts.append(f"{msg.role.title()}: {msg.content}")
                context_parts.append("")
        
        return '\n'.join(context_parts)
    
    def clear_session(self, session_id: str):
        """Clear all context for a session"""
        if session_id in self.conversations:
            del self.conversations[session_id]
        if session_id in self.code_contexts:
            del self.code_contexts[session_id]
        if session_id in self.workspace_contexts:
            del self.workspace_contexts[session_id]
        
        logger.info(f"Cleared session {session_id}")
    
    def cleanup_old_sessions(self):
        """Remove old sessions and contexts"""
        current_time = time.time()
        
        # Clean up conversations
        sessions_to_remove = []
        for session_id, messages in self.conversations.items():
            if not messages or current_time - messages[-1].timestamp > self.max_context_age * 2:
                sessions_to_remove.append(session_id)
        
        for session_id in sessions_to_remove:
            self.clear_session(session_id)
        
        logger.info(f"Cleaned up {len(sessions_to_remove)} old sessions")
    
    def get_session_summary(self, session_id: str) -> Dict[str, Any]:
        """Get a summary of the session context"""
        conversation = self.get_conversation_history(session_id)
        code_contexts = self.get_code_contexts(session_id)
        workspace_context = self.get_workspace_context(session_id)
        
        return {
            "session_id": session_id,
            "conversation_length": len(conversation),
            "code_contexts_count": len(code_contexts),
            "has_workspace_context": workspace_context is not None,
            "last_activity": max(
                [msg.timestamp for msg in conversation] + 
                [ctx.timestamp for ctx in code_contexts] + 
                [0]
            ) if conversation or code_contexts else None
        }
    
    def export_session(self, session_id: str) -> Dict[str, Any]:
        """Export session data for backup or analysis"""
        return {
            "session_id": session_id,
            "conversation": [asdict(msg) for msg in self.get_conversation_history(session_id)],
            "code_contexts": [asdict(ctx) for ctx in self.get_code_contexts(session_id)],
            "workspace_context": asdict(self.get_workspace_context(session_id)) if self.get_workspace_context(session_id) else None
        }
    
    def import_session(self, session_data: Dict[str, Any]):
        """Import session data from backup"""
        session_id = session_data["session_id"]
        
        # Import conversation
        self.conversations[session_id] = [
            ConversationMessage(**msg_data) for msg_data in session_data.get("conversation", [])
        ]
        
        # Import code contexts
        self.code_contexts[session_id] = [
            CodeContext(**ctx_data) for ctx_data in session_data.get("code_contexts", [])
        ]
        
        # Import workspace context
        if session_data.get("workspace_context"):
            self.workspace_contexts[session_id] = WorkspaceContext(**session_data["workspace_context"])
        
        logger.info(f"Imported session {session_id}")
