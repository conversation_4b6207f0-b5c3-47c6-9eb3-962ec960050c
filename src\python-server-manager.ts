import * as vscode from 'vscode';
import { spawn, ChildProcess } from 'child_process';
import * as path from 'path';
import axios from 'axios';

export class PythonServerManager {
    private serverProcess: ChildProcess | null = null;
    private serverPort: number;
    private isServerRunning = false;

    constructor() {
        const config = vscode.workspace.getConfiguration('aiCodeAssistant');
        this.serverPort = config.get('serverPort', 8000);
    }

    async startServer(): Promise<void> {
        if (this.isServerRunning) {
            return;
        }

        try {
            // Check if server is already running
            if (await this.checkServerHealth()) {
                this.isServerRunning = true;
                vscode.window.showInformationMessage('Python AI server is already running');
                return;
            }

            // Get the extension path
            const extensionPath = vscode.extensions.getExtension('ai-code-assistant.ai-code-assistant')?.extensionPath;
            if (!extensionPath) {
                throw new Error('Extension path not found');
            }

            const serverPath = path.join(extensionPath, 'python-backend', 'main.py');
            
            // Start the Python server
            this.serverProcess = spawn('python', [serverPath, '--port', this.serverPort.toString()], {
                cwd: path.join(extensionPath, 'python-backend'),
                stdio: ['pipe', 'pipe', 'pipe']
            });

            if (!this.serverProcess) {
                throw new Error('Failed to start Python server process');
            }

            // Handle server output
            this.serverProcess.stdout?.on('data', (data) => {
                console.log(`Python server stdout: ${data}`);
            });

            this.serverProcess.stderr?.on('data', (data) => {
                console.error(`Python server stderr: ${data}`);
            });

            this.serverProcess.on('close', (code) => {
                console.log(`Python server process exited with code ${code}`);
                this.isServerRunning = false;
                this.serverProcess = null;
            });

            this.serverProcess.on('error', (error) => {
                console.error('Python server process error:', error);
                this.isServerRunning = false;
                this.serverProcess = null;
                vscode.window.showErrorMessage(`Failed to start Python server: ${error.message}`);
            });

            // Wait for server to start
            await this.waitForServerStart();
            this.isServerRunning = true;
            vscode.window.showInformationMessage('Python AI server started successfully');

        } catch (error) {
            vscode.window.showErrorMessage(`Failed to start Python server: ${error}`);
            throw error;
        }
    }

    async stopServer(): Promise<void> {
        if (this.serverProcess) {
            this.serverProcess.kill();
            this.serverProcess = null;
            this.isServerRunning = false;
            vscode.window.showInformationMessage('Python AI server stopped');
        }
    }

    async restartServer(): Promise<void> {
        await this.stopServer();
        await this.startServer();
    }

    private async waitForServerStart(maxAttempts = 30): Promise<void> {
        for (let i = 0; i < maxAttempts; i++) {
            try {
                if (await this.checkServerHealth()) {
                    return;
                }
            } catch (error) {
                // Server not ready yet
            }
            
            // Wait 1 second before next attempt
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
        throw new Error('Server failed to start within timeout period');
    }

    private async checkServerHealth(): Promise<boolean> {
        try {
            const response = await axios.get(`http://localhost:${this.serverPort}/health`, {
                timeout: 2000
            });
            return response.status === 200;
        } catch (error) {
            return false;
        }
    }

    isRunning(): boolean {
        return this.isServerRunning;
    }

    getServerUrl(): string {
        return `http://localhost:${this.serverPort}`;
    }
}
