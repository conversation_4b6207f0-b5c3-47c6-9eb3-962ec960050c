import * as vscode from 'vscode';
import { AIService } from './ai-service';

export class ChatProvider {
    private aiService: AIService;
    private conversationHistory: Array<{ role: string; content: string }> = [];

    constructor(aiService: AIService) {
        this.aiService = aiService;
    }

    async handleChatRequest(
        request: vscode.ChatRequest,
        context: vscode.ChatContext,
        stream: vscode.ChatResponseStream,
        token: vscode.CancellationToken
    ): Promise<void> {
        try {
            // Get current workspace context
            const workspaceContext = await this.getWorkspaceContext();
            
            // Add user message to history
            this.conversationHistory.push({
                role: 'user',
                content: request.prompt
            });

            // Prepare context for AI
            const contextString = this.buildContextString(workspaceContext, request);

            // Get AI response
            const response = await this.aiService.chatWithAI(request.prompt, contextString);

            // Add AI response to history
            this.conversationHistory.push({
                role: 'assistant',
                content: response
            });

            // Stream the response
            stream.markdown(response);

        } catch (error) {
            stream.markdown(`❌ Error: ${error}`);
        }
    }

    async provideFollowups(
        result: vscode.ChatResult,
        context: vscode.ChatContext,
        token: vscode.CancellationToken
    ): Promise<vscode.ChatFollowup[]> {
        const followups: vscode.ChatFollowup[] = [];

        // Suggest relevant follow-up actions based on the conversation
        if (this.conversationHistory.length > 0) {
            const lastMessage = this.conversationHistory[this.conversationHistory.length - 1];
            
            if (lastMessage.content.includes('function') || lastMessage.content.includes('method')) {
                followups.push({
                    prompt: 'Generate tests for this function',
                    label: '🧪 Generate Tests',
                    command: 'aiCodeAssistant.generateTests'
                });
            }

            if (lastMessage.content.includes('bug') || lastMessage.content.includes('error')) {
                followups.push({
                    prompt: 'Help me fix this bug',
                    label: '🐛 Fix Bug',
                    command: 'aiCodeAssistant.fixBugs'
                });
            }

            if (lastMessage.content.includes('refactor') || lastMessage.content.includes('improve')) {
                followups.push({
                    prompt: 'Refactor this code',
                    label: '🔧 Refactor',
                    command: 'aiCodeAssistant.refactorCode'
                });
            }

            followups.push({
                prompt: 'Explain this code in detail',
                label: '📖 Explain Code',
                command: 'aiCodeAssistant.explainCode'
            });

            followups.push({
                prompt: 'Generate documentation',
                label: '📝 Generate Docs',
                command: 'aiCodeAssistant.generateDocs'
            });
        }

        return followups;
    }

    private async getWorkspaceContext(): Promise<string> {
        const context: string[] = [];

        // Get current active editor
        const activeEditor = vscode.window.activeTextEditor;
        if (activeEditor) {
            const document = activeEditor.document;
            context.push(`Current file: ${document.fileName}`);
            context.push(`Language: ${document.languageId}`);
            
            // Get selected text if any
            const selection = activeEditor.selection;
            if (!selection.isEmpty) {
                const selectedText = document.getText(selection);
                context.push(`Selected code:\n\`\`\`${document.languageId}\n${selectedText}\n\`\`\``);
            }
        }

        // Get workspace information
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (workspaceFolders) {
            context.push(`Workspace: ${workspaceFolders[0].name}`);
        }

        // Get recently opened files
        const recentFiles = await this.getRecentFiles();
        if (recentFiles.length > 0) {
            context.push(`Recent files: ${recentFiles.join(', ')}`);
        }

        return context.join('\n');
    }

    private async getRecentFiles(): Promise<string[]> {
        // This is a simplified version - in a real implementation,
        // you might want to track recently opened files
        const activeEditor = vscode.window.activeTextEditor;
        if (activeEditor) {
            return [activeEditor.document.fileName.split('/').pop() || ''];
        }
        return [];
    }

    private buildContextString(workspaceContext: string, request: vscode.ChatRequest): string {
        const context: string[] = [];

        // Add workspace context
        if (workspaceContext) {
            context.push('=== Workspace Context ===');
            context.push(workspaceContext);
        }

        // Add conversation history (last 5 messages)
        if (this.conversationHistory.length > 0) {
            context.push('\n=== Conversation History ===');
            const recentHistory = this.conversationHistory.slice(-5);
            recentHistory.forEach(msg => {
                context.push(`${msg.role}: ${msg.content}`);
            });
        }

        // Add any references from the request
        if (request.references && request.references.length > 0) {
            context.push('\n=== Referenced Files ===');
            request.references.forEach(ref => {
                if (ref.value instanceof vscode.Uri) {
                    context.push(`File: ${ref.value.fsPath}`);
                } else if (typeof ref.value === 'string') {
                    context.push(`Reference: ${ref.value}`);
                }
            });
        }

        return context.join('\n');
    }

    clearHistory(): void {
        this.conversationHistory = [];
    }

    getHistory(): Array<{ role: string; content: string }> {
        return [...this.conversationHistory];
    }
}
