const vscode = require('vscode');
const axios = require('axios');

let statusBarItem;

/**
 * @param {vscode.ExtensionContext} context
 */
function activate(context) {
    console.log('AI Code Assistant is now active!');

    // Create status bar item
    statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
    statusBarItem.text = "$(robot) AI Assistant";
    statusBarItem.tooltip = "AI Code Assistant - Click to chat";
    statusBarItem.command = 'aiCodeAssistant.startChat';
    statusBarItem.show();

    // Register commands
    const commands = [
        vscode.commands.registerCommand('aiCodeAssistant.startChat', async () => {
            const prompt = await vscode.window.showInputBox({
                prompt: 'Ask the AI assistant anything about your code',
                placeHolder: 'e.g., "Explain this function" or "How do I optimize this code?"'
            });

            if (prompt) {
                await chatWithAI(prompt);
            }
        }),

        vscode.commands.registerCommand('aiCodeAssistant.explainCode', async () => {
            const editor = vscode.window.activeTextEditor;
            if (!editor) {
                vscode.window.showErrorMessage('No active editor found');
                return;
            }

            const selection = editor.selection;
            const selectedText = editor.document.getText(selection);
            
            if (!selectedText) {
                vscode.window.showErrorMessage('No code selected');
                return;
            }

            const explanation = await analyzeCode(selectedText, editor.document.languageId, 'explain');
            
            if (explanation) {
                // Show explanation in a new document
                const doc = await vscode.workspace.openTextDocument({
                    content: `# Code Explanation\n\n${explanation}`,
                    language: 'markdown'
                });
                await vscode.window.showTextDocument(doc);
            }
        }),

        vscode.commands.registerCommand('aiCodeAssistant.refactorCode', async () => {
            const editor = vscode.window.activeTextEditor;
            if (!editor) {
                vscode.window.showErrorMessage('No active editor found');
                return;
            }

            const selection = editor.selection;
            const selectedText = editor.document.getText(selection);
            
            if (!selectedText) {
                vscode.window.showErrorMessage('No code selected');
                return;
            }

            const refactoredCode = await analyzeCode(selectedText, editor.document.languageId, 'refactor');
            
            if (refactoredCode) {
                const choice = await vscode.window.showInformationMessage(
                    'Replace selected code with refactored version?',
                    'Yes', 'No', 'Show Diff'
                );

                if (choice === 'Yes') {
                    await editor.edit(editBuilder => {
                        editBuilder.replace(selection, refactoredCode);
                    });
                } else if (choice === 'Show Diff') {
                    // Show diff in new document
                    const doc = await vscode.workspace.openTextDocument({
                        content: `# Original Code\n\`\`\`${editor.document.languageId}\n${selectedText}\n\`\`\`\n\n# Refactored Code\n\`\`\`${editor.document.languageId}\n${refactoredCode}\n\`\`\``,
                        language: 'markdown'
                    });
                    await vscode.window.showTextDocument(doc);
                }
            }
        }),

        vscode.commands.registerCommand('aiCodeAssistant.generateTests', async () => {
            const editor = vscode.window.activeTextEditor;
            if (!editor) {
                vscode.window.showErrorMessage('No active editor found');
                return;
            }

            const selection = editor.selection;
            const selectedText = editor.document.getText(selection);
            
            if (!selectedText) {
                vscode.window.showErrorMessage('No code selected');
                return;
            }

            const tests = await analyzeCode(selectedText, editor.document.languageId, 'test');
            
            if (tests) {
                // Create new test file
                const fileName = editor.document.fileName.split('/').pop() || 'code';
                const testFileName = `test_${fileName.replace(/\.[^/.]+$/, '')}_test.${editor.document.languageId}`;
                
                const doc = await vscode.workspace.openTextDocument({
                    content: tests,
                    language: editor.document.languageId
                });
                await vscode.window.showTextDocument(doc);
            }
        }),

        vscode.commands.registerCommand('aiCodeAssistant.fixBugs', async () => {
            const editor = vscode.window.activeTextEditor;
            if (!editor) {
                vscode.window.showErrorMessage('No active editor found');
                return;
            }

            const selection = editor.selection;
            const selectedText = editor.document.getText(selection);
            
            if (!selectedText) {
                vscode.window.showErrorMessage('No code selected');
                return;
            }

            const fixedCode = await analyzeCode(selectedText, editor.document.languageId, 'fix');
            
            if (fixedCode) {
                const choice = await vscode.window.showInformationMessage(
                    'Replace selected code with fixed version?',
                    'Yes', 'No', 'Show Diff'
                );

                if (choice === 'Yes') {
                    await editor.edit(editBuilder => {
                        editBuilder.replace(selection, fixedCode);
                    });
                } else if (choice === 'Show Diff') {
                    const doc = await vscode.workspace.openTextDocument({
                        content: `# Original Code\n\`\`\`${editor.document.languageId}\n${selectedText}\n\`\`\`\n\n# Fixed Code\n\`\`\`${editor.document.languageId}\n${fixedCode}\n\`\`\``,
                        language: 'markdown'
                    });
                    await vscode.window.showTextDocument(doc);
                }
            }
        }),

        vscode.commands.registerCommand('aiCodeAssistant.generateDocs', async () => {
            const editor = vscode.window.activeTextEditor;
            if (!editor) {
                vscode.window.showErrorMessage('No active editor found');
                return;
            }

            const selection = editor.selection;
            const selectedText = editor.document.getText(selection);
            
            if (!selectedText) {
                vscode.window.showErrorMessage('No code selected');
                return;
            }

            const documentation = await analyzeCode(selectedText, editor.document.languageId, 'docs');
            
            if (documentation) {
                // Insert documentation above the selected code
                await editor.edit(editBuilder => {
                    editBuilder.insert(selection.start, documentation + '\n');
                });
            }
        })
    ];

    // Add all disposables to context
    context.subscriptions.push(statusBarItem, ...commands);

    // Show activation message
    vscode.window.showInformationMessage('🤖 AI Code Assistant is ready! Click the robot icon or use Ctrl+Shift+A to start.');
}

async function chatWithAI(prompt) {
    try {
        const config = vscode.workspace.getConfiguration('aiCodeAssistant');
        const serverPort = config.get('serverPort', 8000);
        
        // Show progress
        await vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: "AI Assistant is thinking...",
            cancellable: false
        }, async (progress) => {
            const response = await axios.post(`http://localhost:${serverPort}/ai/chat`, {
                prompt: prompt,
                model: "llama3-8b-8192"
            });

            if (response.data && response.data.response) {
                // Show response in output channel
                const outputChannel = vscode.window.createOutputChannel('AI Assistant');
                outputChannel.clear();
                outputChannel.appendLine(`🤖 AI Assistant Response:`);
                outputChannel.appendLine(`Question: ${prompt}`);
                outputChannel.appendLine(`\nAnswer: ${response.data.response}`);
                outputChannel.show();
            }
        });
    } catch (error) {
        vscode.window.showErrorMessage(`AI Assistant Error: ${error.message}`);
        console.error('AI Chat error:', error);
    }
}

async function analyzeCode(code, language, analysisType) {
    try {
        const config = vscode.workspace.getConfiguration('aiCodeAssistant');
        const serverPort = config.get('serverPort', 8000);
        
        return await vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: `AI is analyzing your ${language} code...`,
            cancellable: false
        }, async (progress) => {
            const response = await axios.post(`http://localhost:${serverPort}/ai/analyze`, {
                code: code,
                language: language,
                analysis_type: analysisType
            });

            if (response.data && response.data.result) {
                return response.data.result;
            }
            return null;
        });
    } catch (error) {
        vscode.window.showErrorMessage(`AI Analysis Error: ${error.message}`);
        console.error('AI Analysis error:', error);
        return null;
    }
}

function deactivate() {
    if (statusBarItem) {
        statusBarItem.dispose();
    }
}

module.exports = {
    activate,
    deactivate
};
