import * as vscode from 'vscode';
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './chat-provider';
import { CompletionProvider } from './completion-provider';
import { AIService } from './ai-service';
import { PythonServerManager } from './python-server-manager';

let chatProvider: ChatProvider;
let completionProvider: CompletionProvider;
let aiService: AIService;
let serverManager: PythonServerManager;

export async function activate(context: vscode.ExtensionContext) {
    console.log('AI Code Assistant is now active!');

    // Initialize services
    serverManager = new PythonServerManager();
    aiService = new AIService();
    chatProvider = new ChatProvider(aiService);
    completionProvider = new CompletionProvider(aiService);

    // Start Python backend server
    await serverManager.startServer();

    // Register chat provider
    const chatDisposable = vscode.chat.createChatParticipant('ai-assistant', chatProvider.handleChatRequest.bind(chatProvider));
    chatDisposable.iconPath = vscode.Uri.joinPath(context.extensionUri, 'assets', 'icon.png');
    chatDisposable.followupProvider = {
        provideFollowups: chatProvider.provideFollowups.bind(chatProvider)
    };

    // Register completion provider
    const completionDisposable = vscode.languages.registerInlineCompletionItemProvider(
        { scheme: 'file' },
        completionProvider
    );

    // Register commands
    const commands = [
        vscode.commands.registerCommand('aiCodeAssistant.startChat', () => {
            vscode.commands.executeCommand('workbench.panel.chat.view.copilot.focus');
        }),

        vscode.commands.registerCommand('aiCodeAssistant.explainCode', async () => {
            const editor = vscode.window.activeTextEditor;
            if (!editor) {
                vscode.window.showErrorMessage('No active editor found');
                return;
            }

            const selection = editor.selection;
            const selectedText = editor.document.getText(selection);
            
            if (!selectedText) {
                vscode.window.showErrorMessage('No code selected');
                return;
            }

            const explanation = await aiService.explainCode(selectedText, editor.document.languageId);
            
            // Show explanation in a new document
            const doc = await vscode.workspace.openTextDocument({
                content: explanation,
                language: 'markdown'
            });
            await vscode.window.showTextDocument(doc);
        }),

        vscode.commands.registerCommand('aiCodeAssistant.refactorCode', async () => {
            const editor = vscode.window.activeTextEditor;
            if (!editor) {
                vscode.window.showErrorMessage('No active editor found');
                return;
            }

            const selection = editor.selection;
            const selectedText = editor.document.getText(selection);
            
            if (!selectedText) {
                vscode.window.showErrorMessage('No code selected');
                return;
            }

            const refactoredCode = await aiService.refactorCode(selectedText, editor.document.languageId);
            
            if (refactoredCode) {
                await editor.edit(editBuilder => {
                    editBuilder.replace(selection, refactoredCode);
                });
            }
        }),

        vscode.commands.registerCommand('aiCodeAssistant.generateTests', async () => {
            const editor = vscode.window.activeTextEditor;
            if (!editor) {
                vscode.window.showErrorMessage('No active editor found');
                return;
            }

            const selection = editor.selection;
            const selectedText = editor.document.getText(selection);
            
            if (!selectedText) {
                vscode.window.showErrorMessage('No code selected');
                return;
            }

            const tests = await aiService.generateTests(selectedText, editor.document.languageId);
            
            // Create new test file
            const testFileName = `test_${editor.document.fileName.split('/').pop()?.replace('.py', '_test.py')}`;
            const doc = await vscode.workspace.openTextDocument({
                content: tests,
                language: editor.document.languageId
            });
            await vscode.window.showTextDocument(doc);
        }),

        vscode.commands.registerCommand('aiCodeAssistant.fixBugs', async () => {
            const editor = vscode.window.activeTextEditor;
            if (!editor) {
                vscode.window.showErrorMessage('No active editor found');
                return;
            }

            const selection = editor.selection;
            const selectedText = editor.document.getText(selection);
            
            if (!selectedText) {
                vscode.window.showErrorMessage('No code selected');
                return;
            }

            const fixedCode = await aiService.fixBugs(selectedText, editor.document.languageId);
            
            if (fixedCode) {
                await editor.edit(editBuilder => {
                    editBuilder.replace(selection, fixedCode);
                });
            }
        }),

        vscode.commands.registerCommand('aiCodeAssistant.generateDocs', async () => {
            const editor = vscode.window.activeTextEditor;
            if (!editor) {
                vscode.window.showErrorMessage('No active editor found');
                return;
            }

            const selection = editor.selection;
            const selectedText = editor.document.getText(selection);
            
            if (!selectedText) {
                vscode.window.showErrorMessage('No code selected');
                return;
            }

            const documentation = await aiService.generateDocumentation(selectedText, editor.document.languageId);
            
            // Insert documentation above the selected code
            await editor.edit(editBuilder => {
                editBuilder.insert(selection.start, documentation + '\n');
            });
        })
    ];

    // Add all disposables to context
    context.subscriptions.push(
        chatDisposable,
        completionDisposable,
        ...commands
    );

    // Show activation message
    vscode.window.showInformationMessage('AI Code Assistant is ready!');
}

export function deactivate() {
    if (serverManager) {
        serverManager.stopServer();
    }
}
