# Minimal requirements for free AI models only
# Use this for testing without paid APIs

# Core dependencies
fastapi==0.104.1
uvicorn==0.24.0
pydantic==2.5.0
python-dotenv==1.0.0
aiohttp==3.9.1
requests==2.31.0

# For Hugging Face Transformers (optional - only if using HF provider)
# Uncomment these lines if you want to use Hugging Face models locally:
# transformers==4.36.0
# torch==2.1.0
# accelerate==0.24.0

# Basic code analysis (lightweight)
pygments==2.17.2
jedi==0.19.1

# Note: For Ollama, no additional Python packages needed - just install Ollama separately
# Note: For Groq, only need the API key - no additional packages needed
