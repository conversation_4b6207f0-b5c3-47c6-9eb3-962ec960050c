import * as vscode from 'vscode';
import axios from 'axios';

export interface AIResponse {
    content: string;
    usage?: {
        prompt_tokens: number;
        completion_tokens: number;
        total_tokens: number;
    };
}

export class AIService {
    private apiKey: string;
    private provider: string;
    private model: string;
    private serverPort: number;

    constructor() {
        this.loadConfiguration();
        
        // Listen for configuration changes
        vscode.workspace.onDidChangeConfiguration(e => {
            if (e.affectsConfiguration('aiCodeAssistant')) {
                this.loadConfiguration();
            }
        });
    }

    private loadConfiguration() {
        const config = vscode.workspace.getConfiguration('aiCodeAssistant');
        this.apiKey = config.get('apiKey', '');
        this.provider = config.get('provider', 'openai');
        this.model = config.get('model', 'gpt-4');
        this.serverPort = config.get('serverPort', 8000);
    }

    async explainCode(code: string, language: string): Promise<string> {
        const prompt = `Explain the following ${language} code in detail. Include what it does, how it works, and any important concepts:

\`\`\`${language}
${code}
\`\`\``;

        try {
            const response = await this.callAI(prompt);
            return response.content;
        } catch (error) {
            vscode.window.showErrorMessage(`Error explaining code: ${error}`);
            return 'Error occurred while explaining code.';
        }
    }

    async refactorCode(code: string, language: string): Promise<string | null> {
        const prompt = `Refactor the following ${language} code to improve readability, performance, and maintainability. Only return the refactored code without explanations:

\`\`\`${language}
${code}
\`\`\``;

        try {
            const response = await this.callAI(prompt);
            // Extract code from response (remove markdown formatting if present)
            const codeMatch = response.content.match(/```[\w]*\n([\s\S]*?)\n```/);
            return codeMatch ? codeMatch[1] : response.content.trim();
        } catch (error) {
            vscode.window.showErrorMessage(`Error refactoring code: ${error}`);
            return null;
        }
    }

    async generateTests(code: string, language: string): Promise<string> {
        const prompt = `Generate comprehensive unit tests for the following ${language} code. Include edge cases and error handling:

\`\`\`${language}
${code}
\`\`\``;

        try {
            const response = await this.callAI(prompt);
            return response.content;
        } catch (error) {
            vscode.window.showErrorMessage(`Error generating tests: ${error}`);
            return 'Error occurred while generating tests.';
        }
    }

    async fixBugs(code: string, language: string): Promise<string | null> {
        const prompt = `Analyze the following ${language} code for bugs and fix them. Only return the fixed code without explanations:

\`\`\`${language}
${code}
\`\`\``;

        try {
            const response = await this.callAI(prompt);
            // Extract code from response
            const codeMatch = response.content.match(/```[\w]*\n([\s\S]*?)\n```/);
            return codeMatch ? codeMatch[1] : response.content.trim();
        } catch (error) {
            vscode.window.showErrorMessage(`Error fixing bugs: ${error}`);
            return null;
        }
    }

    async generateDocumentation(code: string, language: string): Promise<string> {
        const prompt = `Generate comprehensive documentation for the following ${language} code. Include docstrings, parameter descriptions, return values, and examples:

\`\`\`${language}
${code}
\`\`\``;

        try {
            const response = await this.callAI(prompt);
            return response.content;
        } catch (error) {
            vscode.window.showErrorMessage(`Error generating documentation: ${error}`);
            return '# Error occurred while generating documentation.';
        }
    }

    async generateCompletion(context: string, language: string): Promise<string[]> {
        const prompt = `Given the following ${language} code context, suggest the next few lines of code that would logically follow. Provide multiple suggestions:

\`\`\`${language}
${context}
\`\`\``;

        try {
            const response = await this.callAI(prompt);
            // Parse multiple suggestions from response
            const suggestions = response.content.split('\n').filter(line => line.trim().length > 0);
            return suggestions.slice(0, 3); // Return top 3 suggestions
        } catch (error) {
            console.error('Error generating completion:', error);
            return [];
        }
    }

    async chatWithAI(message: string, context?: string): Promise<string> {
        let prompt = message;
        
        if (context) {
            prompt = `Context:\n${context}\n\nUser: ${message}`;
        }

        try {
            const response = await this.callAI(prompt);
            return response.content;
        } catch (error) {
            return `Error: ${error}`;
        }
    }

    private async callAI(prompt: string): Promise<AIResponse> {
        if (!this.apiKey && this.provider !== 'local') {
            throw new Error('API key not configured. Please set your API key in settings.');
        }

        switch (this.provider) {
            case 'openai':
                return this.callOpenAI(prompt);
            case 'anthropic':
                return this.callAnthropic(prompt);
            case 'local':
                return this.callLocalServer(prompt);
            default:
                throw new Error(`Unsupported provider: ${this.provider}`);
        }
    }

    private async callOpenAI(prompt: string): Promise<AIResponse> {
        const response = await axios.post('https://api.openai.com/v1/chat/completions', {
            model: this.model,
            messages: [
                { role: 'system', content: 'You are an expert programming assistant.' },
                { role: 'user', content: prompt }
            ],
            max_tokens: 2000,
            temperature: 0.7
        }, {
            headers: {
                'Authorization': `Bearer ${this.apiKey}`,
                'Content-Type': 'application/json'
            }
        });

        return {
            content: response.data.choices[0].message.content,
            usage: response.data.usage
        };
    }

    private async callAnthropic(prompt: string): Promise<AIResponse> {
        const response = await axios.post('https://api.anthropic.com/v1/messages', {
            model: this.model,
            max_tokens: 2000,
            messages: [
                { role: 'user', content: prompt }
            ]
        }, {
            headers: {
                'x-api-key': this.apiKey,
                'Content-Type': 'application/json',
                'anthropic-version': '2023-06-01'
            }
        });

        return {
            content: response.data.content[0].text,
            usage: response.data.usage
        };
    }

    private async callLocalServer(prompt: string): Promise<AIResponse> {
        try {
            const response = await axios.post(`http://localhost:${this.serverPort}/ai/chat`, {
                prompt: prompt,
                model: this.model
            });

            return {
                content: response.data.response
            };
        } catch (error) {
            throw new Error('Local AI server not available. Please start the Python backend.');
        }
    }
}
