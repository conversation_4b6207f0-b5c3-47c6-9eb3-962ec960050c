import * as vscode from 'vscode';
import { AIService } from './ai-service';

export class CompletionProvider implements vscode.InlineCompletionItemProvider {
    private aiService: AIService;
    private lastCompletionTime = 0;
    private completionDelay = 500; // 500ms delay to avoid too frequent requests

    constructor(aiService: AIService) {
        this.aiService = aiService;
    }

    async provideInlineCompletionItems(
        document: vscode.TextDocument,
        position: vscode.Position,
        context: vscode.InlineCompletionContext,
        token: vscode.CancellationToken
    ): Promise<vscode.InlineCompletionItem[] | vscode.InlineCompletionList | null> {
        
        // Check if auto-completion is enabled
        const config = vscode.workspace.getConfiguration('aiCodeAssistant');
        if (!config.get('enableAutoCompletion', true)) {
            return null;
        }

        // Throttle requests to avoid overwhelming the API
        const now = Date.now();
        if (now - this.lastCompletionTime < this.completionDelay) {
            return null;
        }
        this.lastCompletionTime = now;

        try {
            // Get context around the cursor
            const contextRange = this.getContextRange(document, position);
            const contextText = document.getText(contextRange);
            
            // Get the current line and check if we should provide completion
            const currentLine = document.lineAt(position.line);
            const textBeforeCursor = currentLine.text.substring(0, position.character);
            
            // Skip completion for certain cases
            if (this.shouldSkipCompletion(textBeforeCursor, context)) {
                return null;
            }

            // Generate completions
            const suggestions = await this.aiService.generateCompletion(
                contextText,
                document.languageId
            );

            if (suggestions.length === 0) {
                return null;
            }

            // Convert suggestions to inline completion items
            const completionItems = suggestions.map(suggestion => {
                return new vscode.InlineCompletionItem(
                    suggestion,
                    new vscode.Range(position, position)
                );
            });

            return completionItems;

        } catch (error) {
            console.error('Error providing inline completion:', error);
            return null;
        }
    }

    private getContextRange(document: vscode.TextDocument, position: vscode.Position): vscode.Range {
        // Get context from 10 lines before to current position
        const startLine = Math.max(0, position.line - 10);
        const endLine = position.line;
        const endCharacter = position.character;

        return new vscode.Range(
            new vscode.Position(startLine, 0),
            new vscode.Position(endLine, endCharacter)
        );
    }

    private shouldSkipCompletion(textBeforeCursor: string, context: vscode.InlineCompletionContext): boolean {
        // Skip if we're in a string literal
        if (this.isInStringLiteral(textBeforeCursor)) {
            return true;
        }

        // Skip if we're in a comment
        if (this.isInComment(textBeforeCursor)) {
            return true;
        }

        // Skip if the line is very short (less than 3 characters)
        if (textBeforeCursor.trim().length < 3) {
            return true;
        }

        // Skip if triggered by certain characters that don't need completion
        const triggerCharacters = [' ', '\t', '\n', ';', ',', '(', ')', '[', ']', '{', '}'];
        const lastChar = textBeforeCursor.slice(-1);
        if (triggerCharacters.includes(lastChar)) {
            return true;
        }

        return false;
    }

    private isInStringLiteral(text: string): boolean {
        // Simple check for string literals - count quotes
        const singleQuotes = (text.match(/'/g) || []).length;
        const doubleQuotes = (text.match(/"/g) || []).length;
        const backticks = (text.match(/`/g) || []).length;

        return (singleQuotes % 2 === 1) || (doubleQuotes % 2 === 1) || (backticks % 2 === 1);
    }

    private isInComment(text: string): boolean {
        // Check for common comment patterns
        const trimmedText = text.trim();
        return trimmedText.startsWith('//') || 
               trimmedText.startsWith('#') || 
               trimmedText.startsWith('/*') ||
               trimmedText.includes('"""') ||
               trimmedText.includes("'''");
    }
}
