{"name": "ai-code-assistant", "displayName": "AI Code Assistant", "description": "Advanced AI-powered code assistant similar to Cursor and Augment AI", "version": "1.0.0", "publisher": "ai-code-assistant", "engines": {"vscode": "^1.74.0"}, "categories": ["Other", "Machine Learning", "Programming Languages"], "keywords": ["ai", "assistant", "copilot", "code completion", "chat"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "aiCodeAssistant.startChat", "title": "Start AI Chat", "category": "AI Assistant"}, {"command": "aiCodeAssistant.explainCode", "title": "Explain Selected Code", "category": "AI Assistant"}, {"command": "aiCodeAssistant.refactorCode", "title": "Refactor Code", "category": "AI Assistant"}, {"command": "aiCodeAssistant.generateTests", "title": "Generate Tests", "category": "AI Assistant"}, {"command": "aiCodeAssistant.fixBugs", "title": "Fix Bugs", "category": "AI Assistant"}, {"command": "aiCodeAssistant.generateDocs", "title": "Generate Documentation", "category": "AI Assistant"}], "keybindings": [{"command": "aiCodeAssistant.startChat", "key": "ctrl+shift+a", "mac": "cmd+shift+a"}, {"command": "aiCodeAssistant.explainCode", "key": "ctrl+shift+e", "mac": "cmd+shift+e", "when": "editorHasSelection"}], "menus": {"editor/context": [{"command": "aiCodeAssistant.explainCode", "when": "editorHasSelection", "group": "aiassistant@1"}, {"command": "aiCodeAssistant.refactorCode", "when": "editorHasSelection", "group": "aiassistant@2"}, {"command": "aiCodeAssistant.generateTests", "when": "editorHasSelection", "group": "aiassistant@3"}]}, "configuration": {"title": "AI Code Assistant", "properties": {"aiCodeAssistant.apiKey": {"type": "string", "default": "", "description": "API key for AI service (OpenAI, Anthropic, etc.)"}, "aiCodeAssistant.provider": {"type": "string", "enum": ["openai", "anthropic", "local"], "default": "openai", "description": "AI provider to use"}, "aiCodeAssistant.model": {"type": "string", "default": "gpt-4", "description": "AI model to use"}, "aiCodeAssistant.serverPort": {"type": "number", "default": 8000, "description": "Port for the Python backend server"}, "aiCodeAssistant.enableAutoCompletion": {"type": "boolean", "default": true, "description": "Enable AI-powered auto completion"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "package": "vsce package"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "eslint": "^8.28.0", "typescript": "^4.9.4", "@vscode/test-electron": "^2.2.0", "vsce": "^2.15.0"}, "dependencies": {"axios": "^1.6.0", "ws": "^8.14.0"}}