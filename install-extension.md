# Install AI Code Assistant VS Code Extension

Since Node.js is not installed, here are alternative ways to get the VS Code extension working:

## Option 1: Manual Installation (Recommended)

1. **Copy the extension folder to VS Code extensions directory:**

   **Windows:**
   ```
   Copy the entire project folder to:
   %USERPROFILE%\.vscode\extensions\ai-code-assistant-1.0.0\
   ```

   **Mac:**
   ```
   Copy the entire project folder to:
   ~/.vscode/extensions/ai-code-assistant-1.0.0/
   ```

   **Linux:**
   ```
   Copy the entire project folder to:
   ~/.vscode/extensions/ai-code-assistant-1.0.0/
   ```

2. **Install axios dependency manually:**
   - Download axios from: https://unpkg.com/axios@1.6.0/dist/axios.min.js
   - Create `node_modules/axios/` folder in the extension directory
   - Place the axios file there

3. **Restart VS Code**

## Option 2: Install Node.js (Better Long-term)

1. **Download and install Node.js:**
   - Go to https://nodejs.org/
   - Download the LTS version
   - Install it

2. **Then run these commands:**
   ```bash
   npm install
   npm run compile
   ```

3. **Package the extension:**
   ```bash
   npm install -g vsce
   vsce package
   ```

4. **Install the .vsix file in VS Code:**
   - Open VS Code
   - Press Ctrl+Shift+P
   - Type "Extensions: Install from VSIX"
   - Select the generated .vsix file

## Option 3: Use Without Extension (Current Working Setup)

The AI backend is already working perfectly! You can use it through:

1. **API directly:** http://localhost:8000/docs
2. **Demo script:** `python demo.py`
3. **HTTP requests:** Use any HTTP client
4. **Command line:** Use curl or PowerShell

## Current Features Working:

✅ **AI Chat:** Ask questions about programming
✅ **Code Analysis:** Explain, refactor, fix code
✅ **Test Generation:** Create unit tests
✅ **Code Completion:** Get suggestions
✅ **Documentation:** Generate docs

## Quick Test Commands:

```powershell
# Chat with AI
Invoke-RestMethod -Uri "http://localhost:8000/ai/chat" -Method POST -ContentType "application/json" -Body '{"prompt": "How do I write a Python class?", "model": "llama3-8b-8192"}'

# Analyze code
Invoke-RestMethod -Uri "http://localhost:8000/ai/analyze" -Method POST -ContentType "application/json" -Body '{"code": "def hello():\n    print(\"Hello World\")", "language": "python", "analysis_type": "explain"}'
```

The AI assistant is fully functional even without the VS Code extension!
