#!/usr/bin/env python3
"""
AI Code Assistant Backend Server
Provides AI-powered code analysis, completion, and assistance features.
"""

import argparse
import asyncio
import logging
import os
import sys
from contextlib import asynccontextmanager
from typing import Dict, List, Optional

import uvicorn
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

from ai_handler import AIHandler
from code_analyzer import CodeAnalyzer
from context_manager import ContextManager
from language_server import LanguageServer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Global instances
ai_handler: Optional[AIHandler] = None
code_analyzer: Optional[CodeAnalyzer] = None
context_manager: Optional[ContextManager] = None
language_server: Optional[LanguageServer] = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    global ai_handler, code_analyzer, context_manager, language_server
    
    # Startup
    logger.info("Starting AI Code Assistant Backend...")
    
    try:
        ai_handler = AIHandler()
        code_analyzer = CodeAnalyzer()
        context_manager = ContextManager()
        language_server = LanguageServer()
        
        logger.info("All services initialized successfully")
        yield
        
    except Exception as e:
        logger.error(f"Failed to initialize services: {e}")
        raise
    
    # Shutdown
    logger.info("Shutting down AI Code Assistant Backend...")


# Pydantic models for API requests/responses
class ChatRequest(BaseModel):
    prompt: str
    model: Optional[str] = "gpt-4"
    context: Optional[str] = None


class ChatResponse(BaseModel):
    response: str
    usage: Optional[Dict] = None


class CodeAnalysisRequest(BaseModel):
    code: str
    language: str
    analysis_type: str  # "explain", "refactor", "test", "fix", "docs"


class CodeAnalysisResponse(BaseModel):
    result: str
    suggestions: Optional[List[str]] = None


class CompletionRequest(BaseModel):
    code: str
    language: str
    position: Dict[str, int]  # {"line": 0, "character": 0}


class CompletionResponse(BaseModel):
    completions: List[str]


# Create FastAPI app
app = FastAPI(
    title="AI Code Assistant Backend",
    description="Backend server for AI-powered code assistance",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "message": "AI Code Assistant Backend is running"}


@app.post("/ai/chat", response_model=ChatResponse)
async def chat_with_ai(request: ChatRequest):
    """Chat with AI assistant"""
    try:
        if not ai_handler:
            raise HTTPException(status_code=500, detail="AI handler not initialized")
        
        response = await ai_handler.chat(
            prompt=request.prompt,
            model=request.model,
            context=request.context
        )
        
        return ChatResponse(
            response=response.get("content", ""),
            usage=response.get("usage")
        )
        
    except Exception as e:
        logger.error(f"Error in chat endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/ai/analyze", response_model=CodeAnalysisResponse)
async def analyze_code(request: CodeAnalysisRequest):
    """Analyze code with AI"""
    try:
        if not ai_handler or not code_analyzer:
            raise HTTPException(status_code=500, detail="Services not initialized")
        
        # First analyze the code structure
        analysis = await code_analyzer.analyze(request.code, request.language)
        
        # Then get AI insights
        ai_response = await ai_handler.analyze_code(
            code=request.code,
            language=request.language,
            analysis_type=request.analysis_type,
            structure_analysis=analysis
        )
        
        return CodeAnalysisResponse(
            result=ai_response.get("result", ""),
            suggestions=ai_response.get("suggestions", [])
        )
        
    except Exception as e:
        logger.error(f"Error in analyze endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/ai/complete", response_model=CompletionResponse)
async def get_completions(request: CompletionRequest):
    """Get AI-powered code completions"""
    try:
        if not ai_handler or not code_analyzer:
            raise HTTPException(status_code=500, detail="Services not initialized")
        
        # Analyze context around the position
        context = await code_analyzer.get_completion_context(
            code=request.code,
            language=request.language,
            position=request.position
        )
        
        # Get AI completions
        completions = await ai_handler.get_completions(
            context=context,
            language=request.language
        )
        
        return CompletionResponse(completions=completions)
        
    except Exception as e:
        logger.error(f"Error in completion endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time communication"""
    await websocket.accept()
    
    try:
        while True:
            data = await websocket.receive_json()
            
            # Handle different message types
            message_type = data.get("type")
            
            if message_type == "chat":
                response = await ai_handler.chat(
                    prompt=data.get("prompt", ""),
                    context=data.get("context")
                )
                await websocket.send_json({
                    "type": "chat_response",
                    "data": response
                })
                
            elif message_type == "completion":
                completions = await ai_handler.get_completions(
                    context=data.get("context", ""),
                    language=data.get("language", "python")
                )
                await websocket.send_json({
                    "type": "completion_response",
                    "data": {"completions": completions}
                })
                
            else:
                await websocket.send_json({
                    "type": "error",
                    "data": {"message": f"Unknown message type: {message_type}"}
                })
                
    except WebSocketDisconnect:
        logger.info("WebSocket client disconnected")
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        await websocket.close()


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="AI Code Assistant Backend Server")
    parser.add_argument("--host", default="127.0.0.1", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8000, help="Port to bind to")
    parser.add_argument("--reload", action="store_true", help="Enable auto-reload")
    parser.add_argument("--log-level", default="info", help="Log level")
    
    args = parser.parse_args()
    
    # Set log level
    logging.getLogger().setLevel(getattr(logging, args.log_level.upper()))
    
    logger.info(f"Starting server on {args.host}:{args.port}")
    
    # Run the server
    uvicorn.run(
        "main:app",
        host=args.host,
        port=args.port,
        reload=args.reload,
        log_level=args.log_level
    )


if __name__ == "__main__":
    main()
