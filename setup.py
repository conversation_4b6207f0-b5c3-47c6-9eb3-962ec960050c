#!/usr/bin/env python3
"""
Setup script for AI Code Assistant
Automates the installation and configuration process
"""

import os
import sys
import subprocess
import shutil
import json
from pathlib import Path


def run_command(command, cwd=None, check=True):
    """Run a shell command and return the result"""
    print(f"Running: {command}")
    try:
        result = subprocess.run(
            command,
            shell=True,
            cwd=cwd,
            check=check,
            capture_output=True,
            text=True
        )
        if result.stdout:
            print(result.stdout)
        return result
    except subprocess.CalledProcessError as e:
        print(f"Error running command: {e}")
        if e.stderr:
            print(f"Error output: {e.stderr}")
        if check:
            sys.exit(1)
        return e


def check_prerequisites():
    """Check if all prerequisites are installed"""
    print("Checking prerequisites...")
    
    # Check Node.js
    try:
        result = run_command("node --version", check=False)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ Node.js found: {version}")
        else:
            print("❌ Node.js not found. Please install Node.js 16+ from https://nodejs.org/")
            return False
    except:
        print("❌ Node.js not found. Please install Node.js 16+ from https://nodejs.org/")
        return False
    
    # Check npm
    try:
        result = run_command("npm --version", check=False)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ npm found: {version}")
        else:
            print("❌ npm not found. Please install npm")
            return False
    except:
        print("❌ npm not found. Please install npm")
        return False
    
    # Check Python
    try:
        result = run_command("python --version", check=False)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ Python found: {version}")
        else:
            # Try python3
            result = run_command("python3 --version", check=False)
            if result.returncode == 0:
                version = result.stdout.strip()
                print(f"✅ Python found: {version}")
            else:
                print("❌ Python not found. Please install Python 3.8+ from https://python.org/")
                return False
    except:
        print("❌ Python not found. Please install Python 3.8+ from https://python.org/")
        return False
    
    # Check pip
    try:
        result = run_command("pip --version", check=False)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ pip found: {version}")
        else:
            # Try pip3
            result = run_command("pip3 --version", check=False)
            if result.returncode == 0:
                version = result.stdout.strip()
                print(f"✅ pip found: {version}")
            else:
                print("❌ pip not found. Please install pip")
                return False
    except:
        print("❌ pip not found. Please install pip")
        return False
    
    return True


def setup_frontend():
    """Set up the VS Code extension frontend"""
    print("\n📦 Setting up VS Code extension...")
    
    # Install npm dependencies
    run_command("npm install")
    
    # Compile TypeScript
    run_command("npm run compile")
    
    print("✅ Frontend setup complete")


def setup_backend():
    """Set up the Python backend"""
    print("\n🐍 Setting up Python backend...")
    
    backend_dir = Path("python-backend")
    
    # Create virtual environment
    venv_dir = backend_dir / "venv"
    if not venv_dir.exists():
        print("Creating virtual environment...")
        run_command("python -m venv venv", cwd=backend_dir)
    
    # Determine pip command based on OS
    if os.name == 'nt':  # Windows
        pip_cmd = str(venv_dir / "Scripts" / "pip")
        python_cmd = str(venv_dir / "Scripts" / "python")
    else:  # Unix-like
        pip_cmd = str(venv_dir / "bin" / "pip")
        python_cmd = str(venv_dir / "bin" / "python")
    
    # Install Python dependencies
    print("Installing Python dependencies...")
    run_command(f"{pip_cmd} install -r requirements.txt", cwd=backend_dir)
    
    print("✅ Backend setup complete")
    return python_cmd


def setup_environment():
    """Set up environment configuration"""
    print("\n⚙️ Setting up environment configuration...")
    
    env_example = Path("python-backend/.env.example")
    env_file = Path("python-backend/.env")
    
    if not env_file.exists() and env_example.exists():
        shutil.copy(env_example, env_file)
        print("✅ Created .env file from template")
        print("📝 Please edit python-backend/.env with your API keys")
    else:
        print("✅ Environment file already exists")


def create_launch_scripts():
    """Create convenient launch scripts"""
    print("\n📜 Creating launch scripts...")
    
    # Create start script for Windows
    start_bat = """@echo off
echo Starting AI Code Assistant Backend...
cd python-backend
if exist venv\\Scripts\\python.exe (
    venv\\Scripts\\python.exe main.py
) else (
    python main.py
)
"""
    
    with open("start-backend.bat", "w") as f:
        f.write(start_bat)
    
    # Create start script for Unix-like systems
    start_sh = """#!/bin/bash
echo "Starting AI Code Assistant Backend..."
cd python-backend
if [ -f venv/bin/python ]; then
    venv/bin/python main.py
else
    python main.py
fi
"""
    
    with open("start-backend.sh", "w") as f:
        f.write(start_sh)
    
    # Make shell script executable
    if os.name != 'nt':
        os.chmod("start-backend.sh", 0o755)
    
    print("✅ Launch scripts created")


def package_extension():
    """Package the VS Code extension"""
    print("\n📦 Packaging VS Code extension...")
    
    try:
        # Check if vsce is installed
        run_command("vsce --version", check=False)
    except:
        print("Installing vsce...")
        run_command("npm install -g vsce")
    
    # Package the extension
    run_command("vsce package")
    
    # Find the generated .vsix file
    vsix_files = list(Path(".").glob("*.vsix"))
    if vsix_files:
        print(f"✅ Extension packaged: {vsix_files[0]}")
        return vsix_files[0]
    else:
        print("❌ Failed to find packaged extension")
        return None


def print_installation_instructions(vsix_file=None):
    """Print final installation instructions"""
    print("\n🎉 Setup complete!")
    print("\n📋 Next steps:")
    print("1. Configure your AI API keys in python-backend/.env")
    print("2. Start the backend server:")
    if os.name == 'nt':
        print("   - Windows: double-click start-backend.bat")
    else:
        print("   - Unix/Linux/Mac: ./start-backend.sh")
    print("   - Or manually: cd python-backend && python main.py")
    
    if vsix_file:
        print(f"3. Install the VS Code extension:")
        print(f"   - Open VS Code")
        print(f"   - Press Ctrl+Shift+P (Cmd+Shift+P on Mac)")
        print(f"   - Type 'Extensions: Install from VSIX'")
        print(f"   - Select the file: {vsix_file}")
    
    print("\n🔧 Configuration:")
    print("- Open VS Code settings")
    print("- Search for 'AI Code Assistant'")
    print("- Configure your preferred AI provider and model")
    
    print("\n🚀 Usage:")
    print("- Press Ctrl+Shift+A to start AI chat")
    print("- Right-click on code for AI-powered actions")
    print("- Enjoy AI-powered code completion!")


def main():
    """Main setup function"""
    print("🤖 AI Code Assistant Setup")
    print("=" * 40)
    
    # Check prerequisites
    if not check_prerequisites():
        print("\n❌ Prerequisites check failed. Please install missing components.")
        sys.exit(1)
    
    try:
        # Setup components
        setup_frontend()
        python_cmd = setup_backend()
        setup_environment()
        create_launch_scripts()
        
        # Ask if user wants to package the extension
        response = input("\n📦 Package VS Code extension? (y/n): ").lower().strip()
        vsix_file = None
        if response in ['y', 'yes']:
            vsix_file = package_extension()
        
        # Print final instructions
        print_installation_instructions(vsix_file)
        
    except KeyboardInterrupt:
        print("\n\n⚠️ Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Setup failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
