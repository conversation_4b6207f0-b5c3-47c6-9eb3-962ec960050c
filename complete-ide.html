<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Code Assistant - Complete IDE</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" />
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1e1e1e;
            color: #d4d4d4;
            height: 100vh;
            overflow: hidden;
        }

        .ide-container {
            display: grid;
            grid-template-areas: 
                "titlebar titlebar titlebar"
                "sidebar editor terminal";
            grid-template-columns: 250px 1fr 400px;
            grid-template-rows: 35px 1fr;
            height: 100vh;
        }

        .titlebar {
            grid-area: titlebar;
            background: #323233;
            display: flex;
            align-items: center;
            padding: 0 15px;
            border-bottom: 1px solid #2d2d30;
        }

        .titlebar h1 {
            font-size: 14px;
            color: #cccccc;
            font-weight: normal;
        }

        .sidebar {
            grid-area: sidebar;
            background: #252526;
            border-right: 1px solid #2d2d30;
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 10px;
            background: #2d2d30;
            border-bottom: 1px solid #3e3e42;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .sidebar-title {
            font-size: 11px;
            text-transform: uppercase;
            color: #cccccc;
            font-weight: 600;
        }

        .sidebar-actions {
            display: flex;
            gap: 8px;
        }

        .sidebar-btn {
            background: none;
            border: none;
            color: #cccccc;
            cursor: pointer;
            padding: 4px;
            border-radius: 3px;
            font-size: 12px;
        }

        .sidebar-btn:hover {
            background: #3e3e42;
        }

        .file-explorer {
            flex: 1;
            overflow-y: auto;
            padding: 5px 0;
        }

        .file-tree {
            list-style: none;
            padding: 0;
        }

        .file-item, .folder-item {
            display: flex;
            align-items: center;
            padding: 4px 8px;
            cursor: pointer;
            font-size: 13px;
            color: #cccccc;
            border-left: 3px solid transparent;
            position: relative;
        }

        .file-item:hover, .folder-item:hover {
            background: #2a2d2e;
        }

        .file-item.active {
            background: #094771;
            border-left-color: #007acc;
        }

        .file-item i, .folder-item i {
            margin-right: 6px;
            width: 16px;
            text-align: center;
        }

        .folder-item {
            font-weight: 500;
        }

        .folder-content {
            padding-left: 16px;
        }

        .folder-item.collapsed + .folder-content {
            display: none;
        }

        .context-menu {
            position: fixed;
            background: #2d2d30;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            padding: 4px 0;
            z-index: 1000;
            display: none;
            min-width: 150px;
        }

        .context-menu-item {
            padding: 6px 12px;
            cursor: pointer;
            font-size: 12px;
            color: #cccccc;
        }

        .context-menu-item:hover {
            background: #094771;
        }

        .context-menu-separator {
            height: 1px;
            background: #5a5a5a;
            margin: 4px 0;
        }

        .editor-area {
            grid-area: editor;
            background: #1e1e1e;
            display: flex;
            flex-direction: column;
        }

        .tab-bar {
            background: #2d2d30;
            border-bottom: 1px solid #3e3e42;
            display: flex;
            min-height: 35px;
            overflow-x: auto;
        }

        .tab {
            display: flex;
            align-items: center;
            padding: 0 12px;
            background: #2d2d30;
            border-right: 1px solid #3e3e42;
            cursor: pointer;
            font-size: 13px;
            color: #969696;
            min-width: 120px;
            position: relative;
            white-space: nowrap;
        }

        .tab.active {
            background: #1e1e1e;
            color: #ffffff;
        }

        .tab-close {
            margin-left: 8px;
            opacity: 0;
            transition: opacity 0.2s;
            padding: 2px;
            border-radius: 3px;
        }

        .tab:hover .tab-close {
            opacity: 1;
        }

        .tab-close:hover {
            background: #e81123;
            color: white;
        }

        .editor-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .editor-toolbar {
            background: #2d2d30;
            padding: 8px 12px;
            border-bottom: 1px solid #3e3e42;
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .toolbar-btn {
            background: #0e639c;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 5px;
            transition: background 0.2s;
        }

        .toolbar-btn:hover {
            background: #1177bb;
        }

        .toolbar-btn.success {
            background: #16825d;
        }

        .toolbar-btn.success:hover {
            background: #1e9973;
        }

        .code-editor {
            flex: 1;
            background: #1e1e1e;
            border: none;
            outline: none;
            color: #d4d4d4;
            font-family: 'Fira Code', 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
            padding: 20px;
            resize: none;
            tab-size: 4;
        }

        .terminal-area {
            grid-area: terminal;
            background: #0c0c0c;
            border-left: 1px solid #2d2d30;
            display: flex;
            flex-direction: column;
        }

        .terminal-header {
            background: #2d2d30;
            padding: 8px 12px;
            border-bottom: 1px solid #3e3e42;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .terminal-title {
            font-size: 12px;
            color: #cccccc;
        }

        .terminal-content {
            flex: 1;
            padding: 10px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 12px;
            overflow-y: auto;
            background: #0c0c0c;
            color: #cccccc;
        }

        .terminal-output {
            white-space: pre-wrap;
            margin-bottom: 5px;
        }

        .terminal-output.error {
            color: #f85149;
        }

        .terminal-output.success {
            color: #56d364;
        }

        .ai-panel {
            background: #252526;
            border-top: 1px solid #3e3e42;
            padding: 15px;
            max-height: 250px;
            overflow-y: auto;
        }

        .ai-input {
            width: 100%;
            background: #3c3c3c;
            border: 1px solid #5a5a5a;
            color: #d4d4d4;
            padding: 8px 12px;
            border-radius: 3px;
            font-size: 13px;
            margin-bottom: 10px;
        }

        .ai-response {
            background: #2d2d30;
            border-radius: 5px;
            padding: 12px;
            margin-top: 10px;
            font-size: 12px;
            line-height: 1.4;
        }

        .dialog-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
            display: none;
        }

        .dialog {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #2d2d30;
            border: 1px solid #5a5a5a;
            border-radius: 5px;
            padding: 20px;
            z-index: 1000;
            min-width: 300px;
            display: none;
        }

        .dialog h3 {
            margin-bottom: 15px;
            color: #cccccc;
        }

        .dialog-input {
            width: 100%;
            background: #3c3c3c;
            border: 1px solid #5a5a5a;
            color: #d4d4d4;
            padding: 8px;
            border-radius: 3px;
            margin: 10px 0;
        }

        .dialog-buttons {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 15px;
        }

        .dialog-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }

        .dialog-btn.primary {
            background: #0e639c;
            color: white;
        }

        .dialog-btn.secondary {
            background: #5a5a5a;
            color: white;
        }

        .status-bar {
            background: #007acc;
            color: white;
            padding: 4px 12px;
            font-size: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .empty-editor {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            color: #969696;
            font-size: 14px;
        }

        .empty-editor i {
            font-size: 48px;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .scrollbar::-webkit-scrollbar {
            width: 8px;
        }

        .scrollbar::-webkit-scrollbar-track {
            background: #1e1e1e;
        }

        .scrollbar::-webkit-scrollbar-thumb {
            background: #424242;
            border-radius: 4px;
        }

        .scrollbar::-webkit-scrollbar-thumb:hover {
            background: #4f4f4f;
        }
    </style>
</head>
<body>
    <div class="ide-container">
        <!-- Title Bar -->
        <div class="titlebar">
            <h1>🤖 AI Code Assistant - Complete IDE with File Management</h1>
        </div>

        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <span class="sidebar-title">Explorer</span>
                <div class="sidebar-actions">
                    <button class="sidebar-btn" onclick="showNewFileDialog()" title="New File">
                        <i class="fas fa-file-plus"></i>
                    </button>
                    <button class="sidebar-btn" onclick="showNewFolderDialog()" title="New Folder">
                        <i class="fas fa-folder-plus"></i>
                    </button>
                    <button class="sidebar-btn" onclick="refreshExplorer()" title="Refresh">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            </div>
            <div class="file-explorer scrollbar" id="fileExplorer">
                <ul class="file-tree" id="fileTree">
                    <li class="folder-item" onclick="toggleFolder(this)" data-path="my-project">
                        <i class="fas fa-folder-open"></i>
                        <span>my-project</span>
                    </li>
                    <li class="folder-content">
                        <div class="file-item" onclick="openFile('my-project/main.py', 'python')" data-path="my-project/main.py">
                            <i class="fab fa-python" style="color: #3776ab;"></i>
                            <span>main.py</span>
                        </div>
                        <div class="file-item" onclick="openFile('my-project/app.js', 'javascript')" data-path="my-project/app.js">
                            <i class="fab fa-js-square" style="color: #f7df1e;"></i>
                            <span>app.js</span>
                        </div>
                        <div class="file-item" onclick="openFile('my-project/README.md', 'markdown')" data-path="my-project/README.md">
                            <i class="fab fa-markdown" style="color: #083fa1;"></i>
                            <span>README.md</span>
                        </div>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Editor Area -->
        <div class="editor-area">
            <div class="tab-bar" id="tabBar"></div>
            
            <div class="editor-content">
                <div class="editor-toolbar">
                    <button class="toolbar-btn" onclick="runCode()">
                        <i class="fas fa-play"></i>
                        Run Code
                    </button>
                    <button class="toolbar-btn success" onclick="saveFile()">
                        <i class="fas fa-save"></i>
                        Save
                    </button>
                    <button class="toolbar-btn" onclick="analyzeWithAI()">
                        <i class="fas fa-robot"></i>
                        AI Analyze
                    </button>
                    <select id="languageSelect" onchange="changeLanguage()" style="background: #3c3c3c; color: #d4d4d4; border: 1px solid #5a5a5a; padding: 4px 8px; border-radius: 3px;">
                        <option value="python">Python</option>
                        <option value="javascript">JavaScript</option>
                        <option value="java">Java</option>
                        <option value="cpp">C++</option>
                        <option value="go">Go</option>
                        <option value="rust">Rust</option>
                        <option value="html">HTML</option>
                        <option value="css">CSS</option>
                        <option value="markdown">Markdown</option>
                    </select>
                </div>
                
                <div id="editorContainer">
                    <div class="empty-editor">
                        <i class="fas fa-code"></i>
                        <div>No file selected</div>
                        <div style="font-size: 12px; margin-top: 10px; opacity: 0.7;">
                            Create a new file or open an existing one to start coding
                        </div>
                    </div>
                </div>
            </div>

            <!-- AI Panel -->
            <div class="ai-panel">
                <input type="text" class="ai-input" id="aiInput" placeholder="Ask AI about your code... (Press Enter to send)" onkeypress="handleAIInput(event)">
                <div id="aiResponse" class="ai-response" style="display: none;"></div>
            </div>
        </div>

        <!-- Terminal Area -->
        <div class="terminal-area">
            <div class="terminal-header">
                <span class="terminal-title">Terminal</span>
                <button class="toolbar-btn" onclick="clearTerminal()" style="font-size: 10px; padding: 4px 8px;">
                    <i class="fas fa-trash"></i>
                    Clear
                </button>
            </div>
            <div class="terminal-content scrollbar" id="terminalContent">
                <div class="terminal-output">🚀 AI Code Assistant Terminal Ready</div>
                <div class="terminal-output">💡 Create files and folders using the Explorer panel</div>
                <div class="terminal-output">🤖 Use AI features to get help with your code</div>
                <div class="terminal-output">─────────────────────────────────────────</div>
            </div>
        </div>
    </div>

    <!-- Context Menu -->
    <div class="context-menu" id="contextMenu">
        <div class="context-menu-item" onclick="contextAction('newFile')">
            <i class="fas fa-file-plus"></i> New File
        </div>
        <div class="context-menu-item" onclick="contextAction('newFolder')">
            <i class="fas fa-folder-plus"></i> New Folder
        </div>
        <div class="context-menu-separator"></div>
        <div class="context-menu-item" onclick="contextAction('rename')">
            <i class="fas fa-edit"></i> Rename
        </div>
        <div class="context-menu-item" onclick="contextAction('delete')">
            <i class="fas fa-trash"></i> Delete
        </div>
    </div>

    <!-- Dialogs -->
    <div class="dialog-overlay" id="dialogOverlay" onclick="hideDialogs()"></div>
    
    <!-- New File Dialog -->
    <div class="dialog" id="newFileDialog">
        <h3>Create New File</h3>
        <input type="text" class="dialog-input" id="newFileName" placeholder="Enter file name (e.g., script.py)">
        <div class="dialog-buttons">
            <button class="dialog-btn secondary" onclick="hideDialogs()">Cancel</button>
            <button class="dialog-btn primary" onclick="createNewFile()">Create</button>
        </div>
    </div>

    <!-- New Folder Dialog -->
    <div class="dialog" id="newFolderDialog">
        <h3>Create New Folder</h3>
        <input type="text" class="dialog-input" id="newFolderName" placeholder="Enter folder name">
        <div class="dialog-buttons">
            <button class="dialog-btn secondary" onclick="hideDialogs()">Cancel</button>
            <button class="dialog-btn primary" onclick="createNewFolder()">Create</button>
        </div>
    </div>

    <!-- Rename Dialog -->
    <div class="dialog" id="renameDialog">
        <h3>Rename</h3>
        <input type="text" class="dialog-input" id="renameInput" placeholder="Enter new name">
        <div class="dialog-buttons">
            <button class="dialog-btn secondary" onclick="hideDialogs()">Cancel</button>
            <button class="dialog-btn primary" onclick="performRename()">Rename</button>
        </div>
    </div>

    <div class="status-bar">
        <span id="statusLeft">Ready</span>
        <span id="statusRight">No file selected</span>
    </div>

    <script>
        // IDE State Management
        const IDE = {
            files: new Map(),
            folders: new Set(['my-project']),
            activeFile: null,
            apiBase: 'http://localhost:8000',
            contextTarget: null,

            init() {
                // Initialize default files
                this.files.set('my-project/main.py', {
                    content: `# Python Main File
print("Hello, AI Code Assistant!")
print("Welcome to your new project!")

def greet(name):
    """Greet a person by name"""
    return f"Hello, {name}!"

def main():
    """Main function"""
    name = "Developer"
    message = greet(name)
    print(message)

    # Example: Calculate factorial
    def factorial(n):
        if n <= 1:
            return 1
        return n * factorial(n - 1)

    print("\\nFactorial examples:")
    for i in range(1, 6):
        print(f"{i}! = {factorial(i)}")

if __name__ == "__main__":
    main()`,
                    language: 'python',
                    saved: true
                });

                this.files.set('my-project/app.js', {
                    content: `// JavaScript Application
console.log("Hello from JavaScript!");

class Calculator {
    constructor() {
        this.history = [];
    }

    add(a, b) {
        const result = a + b;
        this.history.push(\`\${a} + \${b} = \${result}\`);
        return result;
    }

    multiply(a, b) {
        const result = a * b;
        this.history.push(\`\${a} * \${b} = \${result}\`);
        return result;
    }

    getHistory() {
        return this.history;
    }
}

// Example usage
const calc = new Calculator();
console.log("Addition:", calc.add(5, 3));
console.log("Multiplication:", calc.multiply(4, 7));
console.log("History:", calc.getHistory());

// Array operations
const numbers = [1, 2, 3, 4, 5];
const doubled = numbers.map(n => n * 2);
console.log("Original:", numbers);
console.log("Doubled:", doubled);`,
                    language: 'javascript',
                    saved: true
                });

                this.files.set('my-project/README.md', {
                    content: `# My AI Code Assistant Project

Welcome to your new project created with AI Code Assistant!

## Features

- 🤖 **AI-Powered Development** - Get intelligent code suggestions and analysis
- 📁 **File Management** - Create, edit, and organize your project files
- ⚡ **Code Execution** - Run your code directly in the IDE
- 💬 **AI Chat** - Ask questions and get help while coding

## Getting Started

1. **Explore the Files** - Check out the sample Python and JavaScript files
2. **Run Code** - Click the "Run Code" button to execute your programs
3. **Create New Files** - Use the + buttons in the Explorer to add files and folders
4. **Ask AI** - Use the AI chat panel for coding help and suggestions

## Project Structure

\`\`\`
my-project/
├── main.py          # Python main file
├── app.js           # JavaScript application
└── README.md        # This file
\`\`\`

## Tips

- **Right-click** on files and folders for more options
- **Use AI Analyze** to get insights about your code
- **Save frequently** with Ctrl+S
- **Ask AI questions** in the chat panel

Happy coding! 🚀`,
                    language: 'markdown',
                    saved: true
                });

                this.updateStatusBar();
                this.setupEventListeners();
            },

            updateStatusBar() {
                const statusLeft = document.getElementById('statusLeft');
                const statusRight = document.getElementById('statusRight');

                if (this.activeFile) {
                    const file = this.files.get(this.activeFile);
                    const fileName = this.activeFile.split('/').pop();
                    statusLeft.textContent = file?.saved ? 'Saved' : 'Modified';
                    statusRight.textContent = \`\${fileName} • \${file?.language || 'text'}\`;
                } else {
                    statusLeft.textContent = 'Ready';
                    statusRight.textContent = 'No file selected';
                }
            },

            addTerminalOutput(text, type = 'normal') {
                const terminal = document.getElementById('terminalContent');
                const output = document.createElement('div');
                output.className = \`terminal-output \${type}\`;
                output.textContent = text;
                terminal.appendChild(output);
                terminal.scrollTop = terminal.scrollHeight;
            },

            setupEventListeners() {
                // Right-click context menu
                document.addEventListener('contextmenu', (e) => {
                    const fileItem = e.target.closest('.file-item, .folder-item');
                    if (fileItem && fileItem.closest('.file-explorer')) {
                        e.preventDefault();
                        this.showContextMenu(e, fileItem);
                    }
                });

                // Click outside to hide context menu
                document.addEventListener('click', () => {
                    this.hideContextMenu();
                });

                // Keyboard shortcuts
                document.addEventListener('keydown', (e) => {
                    if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                        e.preventDefault();
                        saveFile();
                    }
                    if (e.key === 'F5') {
                        e.preventDefault();
                        runCode();
                    }
                    if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
                        e.preventDefault();
                        showNewFileDialog();
                    }
                });
            },

            showContextMenu(event, target) {
                const contextMenu = document.getElementById('contextMenu');
                this.contextTarget = target;

                contextMenu.style.display = 'block';
                contextMenu.style.left = event.pageX + 'px';
                contextMenu.style.top = event.pageY + 'px';
            },

            hideContextMenu() {
                const contextMenu = document.getElementById('contextMenu');
                contextMenu.style.display = 'none';
                this.contextTarget = null;
            }
        };

        // File Management Functions
        function openFile(filePath, language) {
            // Save current file if editing
            if (IDE.activeFile) {
                const currentContent = document.getElementById('codeEditor')?.value || '';
                const currentFile = IDE.files.get(IDE.activeFile);
                if (currentFile) {
                    currentFile.content = currentContent;
                    currentFile.saved = currentFile.content === currentContent;
                }
            }

            // Switch to new file
            IDE.activeFile = filePath;
            const file = IDE.files.get(filePath);

            if (file) {
                showEditor();
                document.getElementById('codeEditor').value = file.content;
                document.getElementById('languageSelect').value = file.language;
            }

            // Update UI
            updateActiveFileInExplorer(filePath);
            addTabIfNotExists(filePath, language);
            updateActiveTab(filePath);
            IDE.updateStatusBar();
        }

        function showEditor() {
            const container = document.getElementById('editorContainer');
            container.innerHTML = \`
                <textarea class="code-editor scrollbar" id="codeEditor" placeholder="Start coding here..."></textarea>
            \`;

            // Add input listener for auto-save indication
            document.getElementById('codeEditor').addEventListener('input', function() {
                if (IDE.activeFile) {
                    const file = IDE.files.get(IDE.activeFile);
                    if (file) {
                        file.saved = false;
                        IDE.updateStatusBar();
                        updateTabTitle(IDE.activeFile);
                    }
                }
            });
        }

        function addTabIfNotExists(filePath, language) {
            const fileName = filePath.split('/').pop();
            const existingTab = document.querySelector(\`[data-file="\${filePath}"]\`);

            if (!existingTab) {
                const tabBar = document.getElementById('tabBar');
                const tab = document.createElement('div');
                tab.className = 'tab';
                tab.setAttribute('data-file', filePath);
                tab.innerHTML = \`
                    <i class="\${getFileIcon(language)}" style="color: \${getFileColor(language)}; margin-right: 5px;"></i>
                    <span>\${fileName}</span>
                    <i class="fas fa-times tab-close" onclick="closeTab('\${filePath}', event)"></i>
                \`;
                tab.onclick = (e) => {
                    if (!e.target.classList.contains('tab-close')) {
                        openFile(filePath, language);
                    }
                };
                tabBar.appendChild(tab);
            }
        }

        function updateActiveTab(filePath) {
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            const activeTab = document.querySelector(\`[data-file="\${filePath}"]\`);
            if (activeTab) {
                activeTab.classList.add('active');
            }
        }

        function updateTabTitle(filePath) {
            const tab = document.querySelector(\`[data-file="\${filePath}"]\`);
            if (tab) {
                const file = IDE.files.get(filePath);
                const fileName = filePath.split('/').pop();
                const titleSpan = tab.querySelector('span');
                titleSpan.textContent = file?.saved ? fileName : fileName + ' •';
            }
        }

        function closeTab(filePath, event) {
            event.stopPropagation();

            const tab = document.querySelector(\`[data-file="\${filePath}"]\`);
            if (tab) {
                tab.remove();

                // If this was the active tab, switch to another or show empty editor
                if (IDE.activeFile === filePath) {
                    const remainingTabs = document.querySelectorAll('.tab');
                    if (remainingTabs.length > 0) {
                        const firstTab = remainingTabs[0];
                        const newFilePath = firstTab.getAttribute('data-file');
                        const file = IDE.files.get(newFilePath);
                        openFile(newFilePath, file?.language || 'text');
                    } else {
                        // Show empty editor
                        IDE.activeFile = null;
                        document.getElementById('editorContainer').innerHTML = \`
                            <div class="empty-editor">
                                <i class="fas fa-code"></i>
                                <div>No file selected</div>
                                <div style="font-size: 12px; margin-top: 10px; opacity: 0.7;">
                                    Create a new file or open an existing one to start coding
                                </div>
                            </div>
                        \`;
                        IDE.updateStatusBar();
                    }
                }
            }
        }

        function updateActiveFileInExplorer(filePath) {
            document.querySelectorAll('.file-item').forEach(item => {
                item.classList.remove('active');
            });
            const fileItem = document.querySelector(\`[data-path="\${filePath}"]\`);
            if (fileItem) {
                fileItem.classList.add('active');
            }
        }

        // Dialog Functions
        function showNewFileDialog() {
            document.getElementById('dialogOverlay').style.display = 'block';
            document.getElementById('newFileDialog').style.display = 'block';
            document.getElementById('newFileName').focus();
        }

        function showNewFolderDialog() {
            document.getElementById('dialogOverlay').style.display = 'block';
            document.getElementById('newFolderDialog').style.display = 'block';
            document.getElementById('newFolderName').focus();
        }

        function hideDialogs() {
            document.getElementById('dialogOverlay').style.display = 'none';
            document.querySelectorAll('.dialog').forEach(dialog => {
                dialog.style.display = 'none';
            });
            // Clear inputs
            document.getElementById('newFileName').value = '';
            document.getElementById('newFolderName').value = '';
            document.getElementById('renameInput').value = '';
        }

        function createNewFile() {
            const fileName = document.getElementById('newFileName').value.trim();
            if (!fileName) {
                alert('Please enter a file name');
                return;
            }

            const filePath = \`my-project/\${fileName}\`;
            const extension = fileName.split('.').pop().toLowerCase();
            const language = getLanguageFromExtension(extension);

            // Check if file already exists
            if (IDE.files.has(filePath)) {
                alert('File already exists');
                return;
            }

            // Create file
            IDE.files.set(filePath, {
                content: getTemplateForLanguage(language),
                language: language,
                saved: false
            });

            // Add to file explorer
            addFileToExplorer(fileName, language, filePath);

            // Open the new file
            openFile(filePath, language);

            hideDialogs();
            IDE.addTerminalOutput(\`📄 Created new file: \${fileName}\`, 'success');
        }

        function createNewFolder() {
            const folderName = document.getElementById('newFolderName').value.trim();
            if (!folderName) {
                alert('Please enter a folder name');
                return;
            }

            const folderPath = \`my-project/\${folderName}\`;

            // Check if folder already exists
            if (IDE.folders.has(folderPath)) {
                alert('Folder already exists');
                return;
            }

            // Create folder
            IDE.folders.add(folderPath);

            // Add to file explorer
            addFolderToExplorer(folderName, folderPath);

            hideDialogs();
            IDE.addTerminalOutput(\`📁 Created new folder: \${folderName}\`, 'success');
        }

        function addFileToExplorer(fileName, language, filePath) {
            const folderContent = document.querySelector('.folder-content');
            const fileDiv = document.createElement('div');
            fileDiv.className = 'file-item';
            fileDiv.setAttribute('data-path', filePath);
            fileDiv.onclick = () => openFile(filePath, language);
            fileDiv.innerHTML = \`
                <i class="\${getFileIcon(language)}" style="color: \${getFileColor(language)};"></i>
                <span>\${fileName}</span>
            \`;
            folderContent.appendChild(fileDiv);
        }

        function addFolderToExplorer(folderName, folderPath) {
            const folderContent = document.querySelector('.folder-content');
            const folderDiv = document.createElement('div');
            folderDiv.className = 'folder-item';
            folderDiv.setAttribute('data-path', folderPath);
            folderDiv.onclick = function() { toggleFolder(this); };
            folderDiv.innerHTML = \`
                <i class="fas fa-folder"></i>
                <span>\${folderName}</span>
            \`;

            const newFolderContent = document.createElement('div');
            newFolderContent.className = 'folder-content';

            folderContent.appendChild(folderDiv);
            folderContent.appendChild(newFolderContent);
        }

        // Context Menu Actions
        function contextAction(action) {
            const target = IDE.contextTarget;
            if (!target) return;

            const path = target.getAttribute('data-path');

            switch (action) {
                case 'newFile':
                    showNewFileDialog();
                    break;
                case 'newFolder':
                    showNewFolderDialog();
                    break;
                case 'rename':
                    showRenameDialog(path);
                    break;
                case 'delete':
                    deleteItem(path);
                    break;
            }

            IDE.hideContextMenu();
        }

        function showRenameDialog(path) {
            const currentName = path.split('/').pop();
            document.getElementById('renameInput').value = currentName;
            document.getElementById('dialogOverlay').style.display = 'block';
            document.getElementById('renameDialog').style.display = 'block';
            document.getElementById('renameInput').focus();
            document.getElementById('renameDialog').setAttribute('data-path', path);
        }

        function performRename() {
            const dialog = document.getElementById('renameDialog');
            const oldPath = dialog.getAttribute('data-path');
            const newName = document.getElementById('renameInput').value.trim();

            if (!newName) {
                alert('Please enter a new name');
                return;
            }

            const pathParts = oldPath.split('/');
            pathParts[pathParts.length - 1] = newName;
            const newPath = pathParts.join('/');

            if (IDE.files.has(oldPath)) {
                // Rename file
                const fileData = IDE.files.get(oldPath);
                IDE.files.delete(oldPath);
                IDE.files.set(newPath, fileData);

                // Update UI
                const fileItem = document.querySelector(\`[data-path="\${oldPath}"]\`);
                if (fileItem) {
                    fileItem.setAttribute('data-path', newPath);
                    fileItem.querySelector('span').textContent = newName;
                    fileItem.onclick = () => openFile(newPath, fileData.language);
                }

                // Update active file if it's the renamed one
                if (IDE.activeFile === oldPath) {
                    IDE.activeFile = newPath;
                    IDE.updateStatusBar();
                }

                // Update tab if exists
                const tab = document.querySelector(\`[data-file="\${oldPath}"]\`);
                if (tab) {
                    tab.setAttribute('data-file', newPath);
                    tab.querySelector('span').textContent = newName;
                }

                IDE.addTerminalOutput(\`📝 Renamed file: \${oldPath.split('/').pop()} → \${newName}\`, 'success');
            }

            hideDialogs();
        }

        function deleteItem(path) {
            if (!confirm(\`Are you sure you want to delete "\${path.split('/').pop()}"?\`)) {
                return;
            }

            if (IDE.files.has(path)) {
                // Delete file
                IDE.files.delete(path);

                // Remove from UI
                const fileItem = document.querySelector(\`[data-path="\${path}"]\`);
                if (fileItem) {
                    fileItem.remove();
                }

                // Close tab if open
                const tab = document.querySelector(\`[data-file="\${path}"]\`);
                if (tab) {
                    closeTab(path, { stopPropagation: () => {} });
                }

                IDE.addTerminalOutput(\`🗑️ Deleted file: \${path.split('/').pop()}\`, 'success');
            }
        }

        // Utility Functions
        function getFileIcon(language) {
            const icons = {
                'python': 'fab fa-python',
                'javascript': 'fab fa-js-square',
                'java': 'fab fa-java',
                'html': 'fab fa-html5',
                'css': 'fab fa-css3-alt',
                'markdown': 'fab fa-markdown',
                'json': 'fas fa-code',
                'xml': 'fas fa-code',
                'sql': 'fas fa-database',
                'cpp': 'fas fa-code',
                'c': 'fas fa-code',
                'go': 'fas fa-code',
                'rust': 'fas fa-code'
            };
            return icons[language] || 'fas fa-file-code';
        }

        function getFileColor(language) {
            const colors = {
                'python': '#3776ab',
                'javascript': '#f7df1e',
                'java': '#ed8b00',
                'html': '#e34f26',
                'css': '#1572b6',
                'markdown': '#083fa1',
                'json': '#000000',
                'sql': '#336791',
                'cpp': '#00599c',
                'c': '#a8b9cc',
                'go': '#00add8',
                'rust': '#ce422b'
            };
            return colors[language] || '#cccccc';
        }

        function getLanguageFromExtension(ext) {
            const extensions = {
                'py': 'python',
                'js': 'javascript',
                'ts': 'typescript',
                'java': 'java',
                'cpp': 'cpp',
                'c': 'c',
                'cs': 'csharp',
                'go': 'go',
                'rs': 'rust',
                'html': 'html',
                'css': 'css',
                'md': 'markdown',
                'json': 'json',
                'xml': 'xml',
                'sql': 'sql'
            };
            return extensions[ext] || 'text';
        }

        function getTemplateForLanguage(language) {
            const templates = {
                'python': \`# Python Script
def main():
    print("Hello, World!")

if __name__ == "__main__":
    main()
\`,
                'javascript': \`// JavaScript Application
console.log("Hello, World!");

function main() {
    // Your code here
}

main();
\`,
                'java': \`public class Main {
    public static void main(String[] args) {
        System.out.println("Hello, World!");
    }
}
\`,
                'html': \`<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
    <h1>Hello, World!</h1>
</body>
</html>
\`,
                'css': \`/* CSS Styles */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 20px;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
}
\`,
                'markdown': \`# Title

## Subtitle

This is a markdown document.

- List item 1
- List item 2
- List item 3

\`\`\`python
print("Hello, World!")
\`\`\`
\`
            };
            return templates[language] || '// New file\\n';
        }

        // Code Execution Functions
        async function runCode() {
            if (!IDE.activeFile) {
                IDE.addTerminalOutput('❌ No file selected to run', 'error');
                return;
            }

            const code = document.getElementById('codeEditor').value;
            const language = document.getElementById('languageSelect').value;

            if (!code.trim()) {
                IDE.addTerminalOutput('❌ No code to execute', 'error');
                return;
            }

            IDE.addTerminalOutput(\`▶️ Running \${language} code...\`, 'normal');
            IDE.addTerminalOutput('─'.repeat(50), 'normal');

            try {
                if (language === 'javascript') {
                    runJavaScriptLocally(code);
                } else {
                    await executeOnServer(code, language);
                }
            } catch (error) {
                IDE.addTerminalOutput(\`❌ Execution error: \${error.message}\`, 'error');
            }
        }

        function runJavaScriptLocally(code) {
            try {
                IDE.addTerminalOutput('🟨 Running JavaScript locally...', 'normal');

                const originalLog = console.log;
                const outputs = [];

                console.log = (...args) => {
                    outputs.push(args.map(arg =>
                        typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
                    ).join(' '));
                };

                const result = eval(code);
                console.log = originalLog;

                if (outputs.length > 0) {
                    IDE.addTerminalOutput('📤 Output:', 'normal');
                    outputs.forEach(output => IDE.addTerminalOutput(output, 'success'));
                } else if (result !== undefined) {
                    IDE.addTerminalOutput('📤 Return value:', 'normal');
                    IDE.addTerminalOutput(String(result), 'success');
                }

                IDE.addTerminalOutput('✅ JavaScript execution completed', 'success');

            } catch (error) {
                IDE.addTerminalOutput(\`❌ JavaScript Error: \${error.message}\`, 'error');
            }
        }

        async function executeOnServer(code, language) {
            try {
                IDE.addTerminalOutput('🔄 Connecting to execution server...', 'normal');

                const response = await fetch(\`\${IDE.apiBase}/ai/execute\`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        code: code,
                        language: language,
                        input_data: ""
                    })
                });

                if (response.ok) {
                    const result = await response.json();

                    if (result.success) {
                        if (result.output) {
                            IDE.addTerminalOutput('📤 Output:', 'normal');
                            IDE.addTerminalOutput(result.output, 'success');
                        } else {
                            IDE.addTerminalOutput('✅ Code executed successfully (no output)', 'success');
                        }
                        IDE.addTerminalOutput(\`⏱️ Execution time: \${result.execution_time}s\`, 'normal');
                    } else {
                        IDE.addTerminalOutput('❌ Execution failed:', 'error');
                        IDE.addTerminalOutput(result.error || 'Unknown error', 'error');
                    }
                } else {
                    throw new Error(\`Server error \${response.status}\`);
                }
            } catch (error) {
                IDE.addTerminalOutput(\`🚫 Server error: \${error.message}\`, 'error');
                IDE.addTerminalOutput('💡 Make sure the Python backend is running on port 8000', 'normal');
            }
        }

        // AI Functions
        async function analyzeWithAI() {
            if (!IDE.activeFile) {
                IDE.addTerminalOutput('❌ No file selected to analyze', 'error');
                return;
            }

            const code = document.getElementById('codeEditor').value;
            const language = document.getElementById('languageSelect').value;

            if (!code.trim()) {
                IDE.addTerminalOutput('❌ No code to analyze', 'error');
                return;
            }

            try {
                IDE.addTerminalOutput('🤖 AI is analyzing your code...', 'normal');

                const response = await fetch(\`\${IDE.apiBase}/ai/analyze\`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        code: code,
                        language: language,
                        analysis_type: 'explain'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    showAIResponse(\`Code Analysis:\\n\\n\${data.result}\`);
                } else {
                    throw new Error(\`Server error: \${response.status}\`);
                }
            } catch (error) {
                IDE.addTerminalOutput(\`❌ AI analysis error: \${error.message}\`, 'error');
                showAIResponse(\`Error analyzing code: \${error.message}\`);
            }
        }

        async function handleAIInput(event) {
            if (event.key === 'Enter') {
                const input = document.getElementById('aiInput');
                const prompt = input.value.trim();

                if (!prompt) return;

                input.value = '';
                showAIResponse(\`You: \${prompt}\`, false);

                try {
                    const response = await fetch(\`\${IDE.apiBase}/ai/chat\`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            prompt: prompt,
                            model: 'llama3-8b-8192'
                        })
                    });

                    if (response.ok) {
                        const data = await response.json();
                        showAIResponse(\`AI: \${data.response}\`);
                    } else {
                        throw new Error(\`Server error: \${response.status}\`);
                    }
                } catch (error) {
                    showAIResponse(\`Error: \${error.message}\`);
                }
            }
        }

        function showAIResponse(text, isAI = true) {
            const responseDiv = document.getElementById('aiResponse');
            responseDiv.style.display = 'block';

            const messageDiv = document.createElement('div');
            messageDiv.style.marginBottom = '10px';
            messageDiv.style.padding = '8px';
            messageDiv.style.borderRadius = '5px';
            messageDiv.style.backgroundColor = isAI ? '#2d2d30' : '#094771';
            messageDiv.style.whiteSpace = 'pre-wrap';
            messageDiv.textContent = text;

            responseDiv.appendChild(messageDiv);
            responseDiv.scrollTop = responseDiv.scrollHeight;
        }

        // Other Functions
        function saveFile() {
            if (!IDE.activeFile) {
                IDE.addTerminalOutput('❌ No file selected to save', 'error');
                return;
            }

            const content = document.getElementById('codeEditor').value;
            const file = IDE.files.get(IDE.activeFile);
            if (file) {
                file.content = content;
                file.saved = true;
                updateTabTitle(IDE.activeFile);
                IDE.updateStatusBar();
                IDE.addTerminalOutput(\`💾 Saved: \${IDE.activeFile.split('/').pop()}\`, 'success');
            }
        }

        function changeLanguage() {
            if (!IDE.activeFile) return;

            const language = document.getElementById('languageSelect').value;
            const file = IDE.files.get(IDE.activeFile);
            if (file) {
                file.language = language;
                IDE.updateStatusBar();
            }
        }

        function toggleFolder(element) {
            element.classList.toggle('collapsed');
        }

        function clearTerminal() {
            const terminal = document.getElementById('terminalContent');
            terminal.innerHTML = '';
            IDE.addTerminalOutput('🧹 Terminal cleared', 'normal');
        }

        function refreshExplorer() {
            IDE.addTerminalOutput('🔄 Explorer refreshed', 'normal');
        }

        // Initialize IDE when page loads
        document.addEventListener('DOMContentLoaded', function() {
            IDE.init();
            IDE.addTerminalOutput('🎉 Complete IDE with file management ready!', 'success');
            IDE.addTerminalOutput('💡 Click the + buttons to create new files and folders', 'normal');
            IDE.addTerminalOutput('💡 Right-click on files for more options', 'normal');
        });
    </script>
</body>
</html>
