# AI Code Assistant

A powerful VS Code extension that provides AI-powered code assistance similar to Cursor and Augment AI. Built with TypeScript (frontend) and Python (backend).

## Features

🤖 **AI-Powered Code Completion** - Intelligent code suggestions powered by GPT-4, Claude, or local models
💬 **Interactive Chat Interface** - Chat with AI about your code directly in VS Code
🔍 **Code Analysis & Explanation** - Get detailed explanations of complex code
🛠️ **Smart Refactoring** - AI-suggested code improvements and refactoring
🧪 **Test Generation** - Automatically generate unit tests for your functions
📝 **Documentation Generation** - Create comprehensive documentation
🐛 **Bug Detection & Fixing** - Identify and fix potential bugs
🎯 **Context-Aware Assistance** - Understands your entire codebase context

## Architecture

```
┌─────────────────────┐    ┌─────────────────────┐
│   VS Code Extension │    │   Python Backend    │
│   (TypeScript)      │◄──►│   (FastAPI)         │
├─────────────────────┤    ├─────────────────────┤
│ • Chat Provider     │    │ • AI Handler        │
│ • Completion Provider│   │ • Code Analyzer     │
│ • Command Handlers  │    │ • Context Manager   │
│ • Server Manager    │    │ • Language Server   │
└─────────────────────┘    └─────────────────────┘
                                      │
                           ┌─────────────────────┐
                           │   AI Services       │
                           │ • OpenAI GPT-4      │
                           │ • Anthropic Claude  │
                           │ • Local Models      │
                           └─────────────────────┘
```

## 🚀 Quick Start with Free Models

**Get started in 5 minutes with completely free AI models:**

```bash
# 1. Install Ollama (free local AI)
curl -fsSL https://ollama.ai/install.sh | sh  # Linux/Mac
# Or download from https://ollama.ai/ for Windows

# 2. Pull a coding model
ollama pull codellama:7b

# 3. Setup the project
git clone <your-repo>
cd ai-code-assistant
cd python-backend
pip install -r requirements-free.txt

# 4. Configure for free models
cp .env.example .env
# Edit .env: set AI_PROVIDER=ollama

# 5. Test the setup
python test_free_models.py

# 6. Start the backend
python main.py
```

**📖 For detailed free model setup, see [FREE_MODELS_SETUP.md](FREE_MODELS_SETUP.md)**

## Installation

### Prerequisites

- Node.js 16+ and npm
- Python 3.8+
- VS Code 1.74+

### Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ai-code-assistant
   ```

2. **Install VS Code extension dependencies**
   ```bash
   npm install
   ```

3. **Set up Python backend**
   ```bash
   cd python-backend
   pip install -r requirements.txt
   ```

4. **Configure AI services**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys
   ```

5. **Build the extension**
   ```bash
   npm run compile
   ```

6. **Install the extension**
   - Open VS Code
   - Press `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac)
   - Type "Extensions: Install from VSIX"
   - Select the generated `.vsix` file

## Configuration

### 🆓 Free AI Providers (Recommended for Testing)

#### Ollama (Local, Free) - **RECOMMENDED**
1. Install [Ollama](https://ollama.ai/)
2. Pull a code model: `ollama pull codellama:7b`
3. Configure in `.env`:
   ```
   AI_PROVIDER=ollama
   AI_MODEL=codellama:7b
   OLLAMA_URL=http://localhost:11434
   ```

#### Groq (Free API with Rate Limits)
1. Get a free API key from [Groq Console](https://console.groq.com/)
2. Add to your `.env` file:
   ```
   AI_PROVIDER=groq
   AI_MODEL=mixtral-8x7b-32768
   GROQ_API_KEY=your_free_key_here
   ```

#### Hugging Face Transformers (Local, Free)
1. Install transformers: `pip install transformers torch`
2. Configure in `.env`:
   ```
   AI_PROVIDER=huggingface
   HF_MODEL=microsoft/DialoGPT-medium
   ```

### 💰 Paid AI Providers (Better Performance)

#### OpenAI
1. Get an API key from [OpenAI](https://platform.openai.com/api-keys)
2. Add to your `.env` file:
   ```
   AI_PROVIDER=openai
   AI_MODEL=gpt-4
   OPENAI_API_KEY=your_key_here
   ```

#### Anthropic Claude
1. Get an API key from [Anthropic](https://console.anthropic.com/)
2. Add to your `.env` file:
   ```
   AI_PROVIDER=anthropic
   AI_MODEL=claude-3-sonnet-20240229
   ANTHROPIC_API_KEY=your_key_here
   ```

### VS Code Settings

Open VS Code settings and configure:

```json
{
  "aiCodeAssistant.apiKey": "your_api_key",
  "aiCodeAssistant.provider": "openai",
  "aiCodeAssistant.model": "gpt-4",
  "aiCodeAssistant.enableAutoCompletion": true,
  "aiCodeAssistant.serverPort": 8000
}
```

## Usage

### Chat Interface
- Press `Ctrl+Shift+A` (or `Cmd+Shift+A` on Mac) to open AI chat
- Ask questions about your code, request explanations, or get help with debugging

### Code Actions
- Select code and right-click to see AI-powered options:
  - **Explain Code** - Get detailed explanations
  - **Refactor Code** - Improve code quality
  - **Generate Tests** - Create unit tests
  - **Fix Bugs** - Identify and fix issues
  - **Generate Docs** - Add documentation

### Auto-Completion
- AI-powered suggestions appear as you type
- Press `Tab` to accept suggestions
- Multiple suggestions available with `Ctrl+Space`

### Commands
Access via Command Palette (`Ctrl+Shift+P`):
- `AI Assistant: Start Chat`
- `AI Assistant: Explain Selected Code`
- `AI Assistant: Refactor Code`
- `AI Assistant: Generate Tests`
- `AI Assistant: Fix Bugs`
- `AI Assistant: Generate Documentation`

## Development

### Building from Source

1. **Install dependencies**
   ```bash
   npm install
   cd python-backend && pip install -r requirements.txt
   ```

2. **Start development**
   ```bash
   # Terminal 1: Start Python backend
   cd python-backend
   python main.py --reload

   # Terminal 2: Watch TypeScript compilation
   npm run watch

   # Terminal 3: Run extension in development
   # Press F5 in VS Code to launch Extension Development Host
   ```

### Project Structure

```
ai-code-assistant/
├── src/                          # VS Code extension source
│   ├── extension.ts             # Main extension entry point
│   ├── ai-service.ts            # AI service integration
│   ├── chat-provider.ts         # Chat interface
│   ├── completion-provider.ts   # Code completion
│   └── python-server-manager.ts # Backend server management
├── python-backend/              # Python backend
│   ├── main.py                  # FastAPI server
│   ├── ai_handler.py            # AI model integration
│   ├── code_analyzer.py         # Code analysis engine
│   ├── context_manager.py       # Context management
│   └── language_server.py       # LSP implementation
├── package.json                 # Extension manifest
└── README.md                    # This file
```

### Adding New Features

1. **VS Code Extension Features**
   - Add commands to `package.json`
   - Implement handlers in `src/extension.ts`
   - Create providers for new functionality

2. **Backend Features**
   - Add endpoints to `main.py`
   - Implement logic in appropriate modules
   - Update AI prompts in `ai_handler.py`

## Supported Languages

- ✅ Python
- ✅ JavaScript/TypeScript
- ✅ Java
- ✅ C/C++
- ✅ Go
- ✅ Rust
- ✅ PHP
- ✅ Ruby
- ✅ C#
- ✅ And more...

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details

## Troubleshooting

### Common Issues

**Extension not loading**
- Check VS Code version compatibility
- Ensure all dependencies are installed
- Check the Output panel for errors

**Python backend not starting**
- Verify Python 3.8+ is installed
- Check that all requirements are installed
- Ensure port 8000 is available

**AI responses not working**
- Verify API keys are correctly configured
- Check internet connection
- Review backend logs for errors

**Auto-completion not working**
- Enable auto-completion in settings
- Check that the backend server is running
- Verify the language is supported

### Getting Help

- Check the [Issues](https://github.com/your-repo/issues) page
- Review the logs in VS Code Output panel
- Check Python backend logs

## Roadmap

- [ ] Multi-file context awareness
- [ ] Custom model fine-tuning
- [ ] Team collaboration features
- [ ] Advanced code metrics
- [ ] Integration with more AI providers
- [ ] Mobile development support
- [ ] Real-time collaboration
- [ ] Advanced debugging assistance
