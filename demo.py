#!/usr/bin/env python3
"""
Demo script to showcase the AI Code Assistant capabilities
"""

import requests
import json
import time

# Server URL
BASE_URL = "http://localhost:8000"

def test_ai_chat(prompt):
    """Test the AI chat functionality"""
    print(f"\n🤖 AI Chat: {prompt}")
    print("-" * 50)
    
    response = requests.post(
        f"{BASE_URL}/ai/chat",
        json={
            "prompt": prompt,
            "model": "llama3-8b-8192"
        }
    )
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Response: {result['response']}")
        if result.get('usage'):
            usage = result['usage']
            print(f"📊 Tokens used: {usage.get('total_tokens', 'N/A')}")
    else:
        print(f"❌ Error: {response.status_code} - {response.text}")

def test_code_analysis(code, analysis_type):
    """Test the code analysis functionality"""
    print(f"\n🔍 Code Analysis ({analysis_type})")
    print("-" * 50)
    print(f"Code:\n{code}")
    print()
    
    response = requests.post(
        f"{BASE_URL}/ai/analyze",
        json={
            "code": code,
            "language": "python",
            "analysis_type": analysis_type
        }
    )
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Analysis Result:")
        print(result['result'])
        if result.get('suggestions'):
            print(f"\n💡 Suggestions:")
            for i, suggestion in enumerate(result['suggestions'], 1):
                print(f"  {i}. {suggestion}")
    else:
        print(f"❌ Error: {response.status_code} - {response.text}")

def test_code_completion(context):
    """Test the code completion functionality"""
    print(f"\n⚡ Code Completion")
    print("-" * 50)
    print(f"Context:\n{context}")
    print()
    
    response = requests.post(
        f"{BASE_URL}/ai/complete",
        json={
            "code": context,
            "language": "python",
            "position": {"line": context.count('\n'), "character": len(context.split('\n')[-1])}
        }
    )
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Completions:")
        for i, completion in enumerate(result['completions'], 1):
            print(f"  {i}. {completion}")
    else:
        print(f"❌ Error: {response.status_code} - {response.text}")

def main():
    """Main demo function"""
    print("🚀 AI Code Assistant Demo")
    print("=" * 60)
    
    # Check if server is running
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            print("✅ Server is running and healthy!")
        else:
            print("❌ Server health check failed")
            return
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Make sure it's running on http://localhost:8000")
        return
    
    # Demo 1: Basic AI Chat
    test_ai_chat("Hello! Can you help me with Python programming?")
    
    time.sleep(1)
    
    # Demo 2: Code Explanation
    sample_code = """def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)"""
    
    test_code_analysis(sample_code, "explain")
    
    time.sleep(1)
    
    # Demo 3: Code Refactoring
    messy_code = """def calc(x,y):
    if x>y:
        return x+y
    else:
        return x-y"""
    
    test_code_analysis(messy_code, "refactor")
    
    time.sleep(1)
    
    # Demo 4: Test Generation
    function_code = """def add_numbers(a, b):
    return a + b"""
    
    test_code_analysis(function_code, "test")
    
    time.sleep(1)
    
    # Demo 5: Code Completion
    incomplete_code = """def calculate_average(numbers):
    total = sum(numbers)
    count = len(numbers)
    # Complete this function"""
    
    test_code_completion(incomplete_code)
    
    time.sleep(1)
    
    # Demo 6: Advanced AI Chat
    test_ai_chat("What are the best practices for writing clean Python code?")
    
    print("\n" + "=" * 60)
    print("🎉 Demo Complete!")
    print("\nThe AI Code Assistant is working with:")
    print("✅ Free Groq API (llama3-8b-8192 model)")
    print("✅ Real-time code analysis")
    print("✅ AI-powered chat assistance")
    print("✅ Code completion suggestions")
    print("✅ Refactoring and optimization")
    print("✅ Test generation")
    print("\n🔗 API Documentation: http://localhost:8000/docs")

if __name__ == "__main__":
    main()
