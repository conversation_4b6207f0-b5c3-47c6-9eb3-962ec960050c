<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Code Assistant - Simple IDE</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            background: #1e1e1e;
            color: #d4d4d4;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: #2d2d30;
            padding: 10px 20px;
            border-bottom: 1px solid #3e3e42;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header h1 {
            color: #4facfe;
            font-size: 18px;
        }

        .toolbar {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .btn {
            background: #0e639c;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.2s;
        }

        .btn:hover {
            background: #1177bb;
        }

        .btn.success {
            background: #16825d;
        }

        .btn.success:hover {
            background: #1e9973;
        }

        .main-content {
            flex: 1;
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 1px;
            background: #3e3e42;
        }

        .editor-panel {
            background: #1e1e1e;
            display: flex;
            flex-direction: column;
        }

        .editor-header {
            background: #2d2d30;
            padding: 8px 15px;
            border-bottom: 1px solid #3e3e42;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .language-select {
            background: #3c3c3c;
            color: #d4d4d4;
            border: 1px solid #5a5a5a;
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 12px;
        }

        .code-editor {
            flex: 1;
            background: #1e1e1e;
            border: none;
            outline: none;
            color: #d4d4d4;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
            padding: 20px;
            resize: none;
            tab-size: 4;
        }

        .terminal-panel {
            background: #0c0c0c;
            display: flex;
            flex-direction: column;
        }

        .terminal-header {
            background: #2d2d30;
            padding: 8px 15px;
            border-bottom: 1px solid #3e3e42;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .terminal-title {
            color: #cccccc;
            font-size: 12px;
        }

        .terminal-content {
            flex: 1;
            padding: 15px;
            overflow-y: auto;
            font-size: 12px;
            line-height: 1.4;
        }

        .terminal-output {
            margin-bottom: 5px;
            white-space: pre-wrap;
        }

        .terminal-output.error {
            color: #f85149;
        }

        .terminal-output.success {
            color: #56d364;
        }

        .terminal-output.normal {
            color: #cccccc;
        }

        .ai-panel {
            background: #252526;
            border-top: 1px solid #3e3e42;
            padding: 15px;
        }

        .ai-input {
            width: 100%;
            background: #3c3c3c;
            border: 1px solid #5a5a5a;
            color: #d4d4d4;
            padding: 8px 12px;
            border-radius: 3px;
            font-size: 13px;
            margin-bottom: 10px;
        }

        .ai-response {
            background: #2d2d30;
            border-radius: 5px;
            padding: 12px;
            margin-top: 10px;
            font-size: 12px;
            line-height: 1.4;
            max-height: 200px;
            overflow-y: auto;
        }

        .status-bar {
            background: #007acc;
            color: white;
            padding: 4px 15px;
            font-size: 11px;
            display: flex;
            justify-content: space-between;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🤖 AI Code Assistant - Simple IDE</h1>
        <div class="toolbar">
            <button class="btn" onclick="runCode()">▶️ Run Code</button>
            <button class="btn success" onclick="saveCode()">💾 Save</button>
            <button class="btn" onclick="analyzeCode()">🤖 AI Analyze</button>
            <button class="btn" onclick="clearTerminal()">🗑️ Clear</button>
        </div>
    </div>

    <div class="main-content">
        <div class="editor-panel">
            <div class="editor-header">
                <span>📝 Code Editor</span>
                <select class="language-select" id="languageSelect" onchange="updateLanguage()">
                    <option value="python">Python</option>
                    <option value="javascript">JavaScript</option>
                    <option value="java">Java</option>
                    <option value="cpp">C++</option>
                    <option value="go">Go</option>
                    <option value="rust">Rust</option>
                </select>
            </div>
            <textarea class="code-editor" id="codeEditor" placeholder="Write your code here...">print("Hello, AI Code Assistant!")
print("This is a simple Python example")

# Calculate factorial
def factorial(n):
    if n <= 1:
        return 1
    return n * factorial(n - 1)

# Test the function
for i in range(1, 6):
    print(f"Factorial of {i} is {factorial(i)}")

print("Code execution test complete!")</textarea>
            
            <div class="ai-panel">
                <input type="text" class="ai-input" id="aiInput" placeholder="Ask AI about your code... (Press Enter)" onkeypress="handleAIInput(event)">
                <div id="aiResponse" class="ai-response" style="display: none;"></div>
            </div>
        </div>

        <div class="terminal-panel">
            <div class="terminal-header">
                <span class="terminal-title">📟 Terminal Output</span>
                <button class="btn" onclick="clearTerminal()" style="font-size: 10px; padding: 4px 8px;">Clear</button>
            </div>
            <div class="terminal-content" id="terminalContent">
                <div class="terminal-output normal">🚀 AI Code Assistant Terminal Ready</div>
                <div class="terminal-output normal">💡 Write code in the editor and click "Run Code" to execute</div>
                <div class="terminal-output normal">🤖 Use the AI panel to ask questions about your code</div>
                <div class="terminal-output normal">─────────────────────────────────────────</div>
            </div>
        </div>
    </div>

    <div class="status-bar">
        <span id="statusLeft">Ready</span>
        <span id="statusRight">Python | Line 1, Col 1</span>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        let currentLanguage = 'python';

        // Add terminal output
        function addTerminalOutput(text, type = 'normal') {
            const terminal = document.getElementById('terminalContent');
            const output = document.createElement('div');
            output.className = `terminal-output ${type}`;
            output.textContent = text;
            terminal.appendChild(output);
            terminal.scrollTop = terminal.scrollHeight;
        }

        // Run code function
        async function runCode() {
            const code = document.getElementById('codeEditor').value.trim();
            const language = document.getElementById('languageSelect').value;

            if (!code) {
                addTerminalOutput('❌ No code to execute', 'error');
                return;
            }

            addTerminalOutput(`▶️ Running ${language} code...`, 'normal');
            addTerminalOutput('─'.repeat(40), 'normal');

            try {
                if (language === 'javascript') {
                    // Run JavaScript locally
                    runJavaScriptLocally(code);
                } else {
                    // Try server execution
                    await executeOnServer(code, language);
                }
            } catch (error) {
                addTerminalOutput(`❌ Error: ${error.message}`, 'error');
            }
        }

        // Execute JavaScript locally
        function runJavaScriptLocally(code) {
            try {
                addTerminalOutput('🟨 Running JavaScript locally...', 'normal');
                
                // Capture console output
                const originalLog = console.log;
                const outputs = [];
                
                console.log = (...args) => {
                    outputs.push(args.map(arg => 
                        typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
                    ).join(' '));
                };
                
                // Execute code
                const result = eval(code);
                
                // Restore console.log
                console.log = originalLog;
                
                // Show outputs
                if (outputs.length > 0) {
                    addTerminalOutput('📤 Output:', 'normal');
                    outputs.forEach(output => addTerminalOutput(output, 'success'));
                } else if (result !== undefined) {
                    addTerminalOutput('📤 Return value:', 'normal');
                    addTerminalOutput(String(result), 'success');
                }
                
                addTerminalOutput('✅ JavaScript execution completed', 'success');
                
            } catch (error) {
                addTerminalOutput(`❌ JavaScript Error: ${error.message}`, 'error');
            }
        }

        // Execute on server
        async function executeOnServer(code, language) {
            try {
                addTerminalOutput('🔄 Connecting to execution server...', 'normal');
                
                const response = await fetch(`${API_BASE}/ai/execute`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        code: code,
                        language: language,
                        input_data: ""
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    
                    if (result.success) {
                        if (result.output) {
                            addTerminalOutput('📤 Output:', 'normal');
                            addTerminalOutput(result.output, 'success');
                        } else {
                            addTerminalOutput('✅ Code executed successfully (no output)', 'success');
                        }
                        addTerminalOutput(`⏱️ Execution time: ${result.execution_time}s`, 'normal');
                    } else {
                        addTerminalOutput('❌ Execution failed:', 'error');
                        addTerminalOutput(result.error || 'Unknown error', 'error');
                    }
                } else {
                    throw new Error(`Server error ${response.status}`);
                }
            } catch (error) {
                addTerminalOutput(`🚫 Server error: ${error.message}`, 'error');
                addTerminalOutput('💡 Make sure the Python backend is running on port 8000', 'normal');
            }
        }

        // AI functions
        async function analyzeCode() {
            const code = document.getElementById('codeEditor').value.trim();
            const language = document.getElementById('languageSelect').value;

            if (!code) {
                addTerminalOutput('❌ No code to analyze', 'error');
                return;
            }

            try {
                addTerminalOutput('🤖 AI is analyzing your code...', 'normal');
                
                const response = await fetch(`${API_BASE}/ai/analyze`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        code: code,
                        language: language,
                        analysis_type: 'explain'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    showAIResponse(`Code Analysis:\n\n${data.result}`);
                } else {
                    throw new Error(`Server error: ${response.status}`);
                }
            } catch (error) {
                addTerminalOutput(`❌ AI analysis error: ${error.message}`, 'error');
            }
        }

        async function handleAIInput(event) {
            if (event.key === 'Enter') {
                const input = document.getElementById('aiInput');
                const prompt = input.value.trim();
                
                if (!prompt) return;
                
                input.value = '';
                showAIResponse(`You: ${prompt}`, false);
                
                try {
                    const response = await fetch(`${API_BASE}/ai/chat`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            prompt: prompt,
                            model: 'llama3-8b-8192'
                        })
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        showAIResponse(`AI: ${data.response}`);
                    } else {
                        throw new Error(`Server error: ${response.status}`);
                    }
                } catch (error) {
                    showAIResponse(`Error: ${error.message}`);
                }
            }
        }

        function showAIResponse(text, isAI = true) {
            const responseDiv = document.getElementById('aiResponse');
            responseDiv.style.display = 'block';
            
            const messageDiv = document.createElement('div');
            messageDiv.style.marginBottom = '10px';
            messageDiv.style.padding = '8px';
            messageDiv.style.borderRadius = '5px';
            messageDiv.style.backgroundColor = isAI ? '#2d2d30' : '#094771';
            messageDiv.style.whiteSpace = 'pre-wrap';
            messageDiv.textContent = text;
            
            responseDiv.appendChild(messageDiv);
            responseDiv.scrollTop = responseDiv.scrollHeight;
        }

        // Utility functions
        function saveCode() {
            const code = document.getElementById('codeEditor').value;
            const language = document.getElementById('languageSelect').value;
            
            // Save to localStorage
            localStorage.setItem('aiCodeAssistant_code', code);
            localStorage.setItem('aiCodeAssistant_language', language);
            
            addTerminalOutput('💾 Code saved to browser storage', 'success');
            updateStatus('Saved');
        }

        function clearTerminal() {
            const terminal = document.getElementById('terminalContent');
            terminal.innerHTML = '';
            addTerminalOutput('🧹 Terminal cleared', 'normal');
        }

        function updateLanguage() {
            currentLanguage = document.getElementById('languageSelect').value;
            updateStatus('Ready');
        }

        function updateStatus(message) {
            document.getElementById('statusLeft').textContent = message;
            const language = document.getElementById('languageSelect').value;
            document.getElementById('statusRight').textContent = `${language} | Ready`;
        }

        // Load saved code on startup
        window.addEventListener('load', function() {
            const savedCode = localStorage.getItem('aiCodeAssistant_code');
            const savedLanguage = localStorage.getItem('aiCodeAssistant_language');
            
            if (savedCode) {
                document.getElementById('codeEditor').value = savedCode;
            }
            
            if (savedLanguage) {
                document.getElementById('languageSelect').value = savedLanguage;
                currentLanguage = savedLanguage;
            }
            
            updateStatus('Ready');
            addTerminalOutput('💡 Tip: Try running the sample Python code!', 'normal');
        });

        // Auto-save on typing
        document.getElementById('codeEditor').addEventListener('input', function() {
            updateStatus('Modified');
        });
    </script>
</body>
</html>
